# DoubaoTTS 使用说明

豆包TTS（火山引擎语音合成）的 Python 异步客户端，支持双向流式文本转语音。

## 功能特性

- ✅ 双向流式TTS：支持边发送文本边接收音频
- ✅ 异步处理：完全基于 asyncio 实现
- ✅ 会话管理：支持单个会话处理多个文本片段
- ✅ 上下文管理器：支持 `async with` 语法
- ✅ 并发安全：支持多个会话同时处理
- ✅ 错误处理：完善的异常处理和日志记录

## 快速开始

### 1. 配置

在 `config/config.yaml` 中配置豆包TTS凭证：

```yaml
api_key:
  doubao_tts:
    app_id: "your-app-id"
    access_token: "your-access-token"

tts:
  provider: "doubao"
  speaker: "zh_female_shuangkuaisisi_moon_bigtts"
  audio_format: "mp3"
  sample_rate: 24000
  ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
```

### 2. 基本用法

#### 简单文本转语音

```python
import asyncio
from provider.tts.doubao_tts import DoubaoTTS

async def simple_tts():
    config = {
        'app_id': 'your-app-id',
        'access_token': 'your-access-token',
        'speaker': 'zh_female_shuangkuaisisi_moon_bigtts',
        'audio_format': 'mp3',
        'sample_rate': 24000
    }
    
    tts = DoubaoTTS(config)
    
    # 一次性转换完整文本
    audio_chunks = []
    async for chunk in tts.text_to_speech_stream("你好，我是语嫣！"):
        audio_chunks.append(chunk)
    
    # 保存音频文件
    with open("output.mp3", "wb") as f:
        f.write(b''.join(audio_chunks))

asyncio.run(simple_tts())
```

#### 流式处理（推荐）

适用于LLM流式输出场景，边生成文本边转换语音：

```python
async def streaming_tts():
    config = {
        'app_id': 'your-app-id',
        'access_token': 'your-access-token',
        'speaker': 'zh_female_shuangkuaisisi_moon_bigtts',
        'audio_format': 'mp3',
        'sample_rate': 24000
    }
    
    # 模拟LLM生成的文本片段
    text_segments = ["你好！", "我是", "语嫣，", "很高兴为你服务。"]
    
    async with DoubaoTTS(config) as tts:
        # 创建两个独立任务：发送文本和接收音频
        async def send_text():
            for segment in text_segments:
                await tts.send_text_segment(segment)
                await asyncio.sleep(0.1)  # 模拟LLM延迟
            
            # 重要：通知TTS不会再有更多文本
            await tts._finish_session(tts.ws, tts.session_id)
        
        async def receive_audio():
            audio_chunks = []
            async for chunk in tts.get_audio_stream():
                audio_chunks.append(chunk)
                # 这里可以实时播放音频
            
            # 保存完整音频
            with open("streaming_output.mp3", "wb") as f:
                f.write(b''.join(audio_chunks))
        
        # 并发执行
        await asyncio.gather(send_text(), receive_audio())

asyncio.run(streaming_tts())
```

## API 参考

### DoubaoTTS 类

#### 初始化

```python
tts = DoubaoTTS(config)
```

**参数：**
- `config` (dict): 配置字典，包含以下字段：
  - `app_id` (str): 火山引擎应用ID
  - `access_token` (str): 访问令牌
  - `speaker` (str): 发音人，默认 "zh_female_shuangkuaisisi_moon_bigtts"
  - `audio_format` (str): 音频格式，默认 "mp3"
  - `sample_rate` (int): 采样率，默认 24000
  - `ws_url` (str): WebSocket服务地址

#### 方法

##### `text_to_speech_stream(text: str) -> AsyncGenerator[bytes, None]`

一次性转换完整文本为语音（适用于短文本）。

##### `start_session() -> bool`

启动TTS会话。

##### `send_text_segment(text: str) -> bool`

发送文本片段到活动会话。

##### `get_audio_stream() -> AsyncGenerator[bytes, None]`

获取音频流。

##### `finish_session() -> bool`

结束TTS会话。

#### 上下文管理器

支持 `async with` 语法，自动管理会话生命周期：

```python
async with DoubaoTTS(config) as tts:
    await tts.send_text_segment("文本片段")
    async for chunk in tts.get_audio_stream():
        # 处理音频块
        pass
# 自动调用 finish_session()
```

## 重要注意事项

### 1. 会话管理

- **单个会话处理多个片段**：一个会话可以处理多个文本片段，不需要为每个片段创建新会话
- **必须调用 finish_session**：发送完所有文本后必须调用 `_finish_session()` 通知服务器
- **独立的发送和接收**：文本发送和音频接收是独立的，DoubaoTTS会根据累积的文本决定何时生成音频

### 2. 异步处理模式

```python
# ✅ 正确：独立的发送和接收任务
async def correct_usage():
    async with DoubaoTTS(config) as tts:
        async def send_text():
            for segment in segments:
                await tts.send_text_segment(segment)
            await tts._finish_session(tts.ws, tts.session_id)  # 重要！
        
        async def receive_audio():
            async for chunk in tts.get_audio_stream():
                # 处理音频块
                pass
        
        await asyncio.gather(send_text(), receive_audio())

# ❌ 错误：不要为每个片段创建新会话
async def wrong_usage():
    for segment in segments:
        async with DoubaoTTS(config) as tts:
            await tts.send_text_segment(segment)  # 低效且不正确
```

### 3. 音频流结束判断

音频流会在以下情况自动结束：
- 收到 `EVENT_SessionFinished` 事件
- WebSocket连接关闭
- 发生异常

不需要手动判断音频流是否结束。

### 4. 错误处理

```python
try:
    async with DoubaoTTS(config) as tts:
        # TTS操作
        pass
except Exception as e:
    logger.error(f"TTS处理失败: {e}")
    # 异常时会自动清理资源
```

## 测试和调试

运行测试套件：

```bash
cd provider/tts/test
python test_doubao_tts.py
```

测试包括：
- 流式TTS测试（单会话多片段）
- 多会话并发测试
- 音频播放验证

## 常见问题

### Q: 最后一段音频没有播放完就结束了？
A: 确保在音频接收完成后给播放器足够时间播放完缓冲区中的音频。测试代码中已经处理了这个问题。

### Q: 为什么有时收不到音频？
A: DoubaoTTS会根据累积的文本片段决定何时生成音频，不是每个文本片段都会立即产生音频。这是正常的行为。

### Q: 如何知道所有音频都生成完了？
A: 调用 `_finish_session()` 后，`get_audio_stream()` 会在收到 `EVENT_SessionFinished` 事件时自动结束。

### Q: 可以并发处理多个TTS任务吗？
A: 可以，每个 `DoubaoTTS` 实例使用独立的WebSocket连接和会话ID，支持并发处理。

### Q: 支持哪些音频格式？
A: 支持 mp3、ogg_opus、pcm 格式，推荐使用 mp3。

## 获取凭证

1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 开通语音合成服务
3. 创建应用获取 APP ID
4. 生成 Access Token

## 更多信息

- [火山引擎TTS文档](https://www.volcengine.com/docs/6561/1329505)
- [双向流式API说明](https://www.volcengine.com/docs/6561/1329505#示例samples)