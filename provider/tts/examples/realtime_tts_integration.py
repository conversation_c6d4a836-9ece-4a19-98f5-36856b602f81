"""
实时TTS集成示例
展示如何在FastReplyAgent中集成实时流式TTS
"""

import asyncio
from typing import AsyncGenerator, List, Optional
from .streaming_tts_manager import StreamingTTSManager, StreamingTTSSession
from logger import get_logger


class RealtimeTTSIntegration:
    """
    实时TTS集成类
    
    演示如何在Agent中使用实时流式TTS
    """
    
    def __init__(self, config, connection_handler):
        self.config = config
        self.connection_handler = connection_handler
        self.logger = get_logger('RealtimeTTSIntegration')
        
        # 初始化流式TTS管理器
        self.tts_manager = StreamingTTSManager(config, connection_handler)
        
        # 当前活跃的TTS会话
        self.current_tts_session: Optional[StreamingTTSSession] = None
    
    async def handle_llm_streaming_response(self, session_id: str, 
                                          llm_response_generator: AsyncGenerator[str, None]) -> str:
        """
        处理LLM流式响应的实时TTS版本
        
        Args:
            session_id: 会话ID
            llm_response_generator: LLM流式响应生成器
            
        Returns:
            str: 完整的响应文本
        """
        accumulated_response = ""
        
        try:
            # 启动实时TTS会话
            tts_session = await self.tts_manager.start_streaming_session(session_id)
            self.current_tts_session = tts_session
            
            self.logger.info(f"Started realtime TTS for session {session_id}")
            
            # 处理LLM流式输出
            chunk_count = 0
            async for chunk in llm_response_generator:
                chunk_count += 1
                accumulated_response += chunk
                
                # 1. 立即发送文本给客户端显示
                try:
                    await self.connection_handler._send_text_tts_sequence(chunk)
                    self.logger.debug(f"Sent text chunk {chunk_count}: '{chunk}'")
                except Exception as e:
                    self.logger.warning(f"Failed to send text chunk: {e}")
                
                # 2. 同时发送给实时TTS处理
                await tts_session.add_text_chunk(chunk)
                
                # 3. 检查是否需要中断
                if self._should_interrupt():
                    self.logger.info("Interruption detected, stopping LLM and TTS")
                    break
            
            # 完成TTS处理
            await tts_session.finalize()
            
            self.logger.info(f"Completed realtime TTS processing: {chunk_count} chunks, "
                           f"{len(accumulated_response)} chars")
            
            return accumulated_response
            
        except Exception as e:
            self.logger.error(f"Error in realtime TTS processing: {e}")
            
            # 清理TTS会话
            if self.current_tts_session:
                await self.current_tts_session.finalize()
            
            # fallback到普通文本响应
            if accumulated_response:
                await self.connection_handler._send_text_tts_sequence(accumulated_response)
            
            return accumulated_response
        
        finally:
            # 清理
            if session_id in self.tts_manager.active_sessions:
                await self.tts_manager.stop_streaming_session(session_id)
            self.current_tts_session = None
    
    def _should_interrupt(self) -> bool:
        """检查是否应该中断处理（连接关闭、用户中断等）"""
        # 这里可以添加各种中断条件检查
        # 例如：连接状态、用户中断信号等
        return False
    
    async def stop_current_tts(self) -> bool:
        """停止当前的TTS处理"""
        if self.current_tts_session:
            await self.current_tts_session.finalize()
            self.current_tts_session = None
            return True
        return False


class EnhancedFastReplyAgent:
    """
    增强版FastReplyAgent示例
    
    展示如何集成实时TTS功能
    """
    
    def __init__(self, config, connection_handler, event_bus):
        self.config = config
        self.connection_handler = connection_handler
        self.event_bus = event_bus
        self.logger = get_logger('EnhancedFastReplyAgent')
        
        # 集成实时TTS
        self.realtime_tts = RealtimeTTSIntegration(config, connection_handler)
        
        # 其他现有组件...
        self.llm_provider = None  # 初始化LLM提供者
        self.connection_closed = False
    
    async def _stream_response_to_client_with_realtime_tts(self, response_generator, session_id: str):
        """
        使用实时TTS的响应流式处理
        
        替换原来的 _stream_response_to_client 方法
        """
        self.logger.info(f"Starting realtime TTS response streaming for session {session_id}")
        
        try:
            # 使用实时TTS处理LLM响应
            final_response = await self.realtime_tts.handle_llm_streaming_response(
                session_id, response_generator
            )
            
            self.logger.info(f"Realtime TTS streaming completed. Final response length: {len(final_response)}")
            return final_response
            
        except Exception as e:
            self.logger.error(f"Error in realtime TTS streaming: {e}")
            raise
    
    async def handle_connection_closed(self):
        """处理连接关闭"""
        self.connection_closed = True
        
        # 停止当前的TTS处理
        await self.realtime_tts.stop_current_tts()
        
        self.logger.info("Connection closed, TTS processing stopped")


# 使用示例
async def example_usage():
    """使用示例"""
    
    # 模拟配置和连接处理器
    config = {
        'tts': {
            'provider': 'doubao',
            'speaker': 'zh_female_shuangkuaisisi_moon_bigtts',
            'audio_format': 'mp3',
            'sample_rate': 24000
        },
        'api_key': {
            'doubao_tts': {
                'app_id': 'your-app-id',
                'access_token': 'your-access-token'
            }
        }
    }
    
    connection_handler = None  # 实际的连接处理器
    
    # 创建实时TTS集成
    realtime_tts = RealtimeTTSIntegration(config, connection_handler)
    
    # 模拟LLM流式响应
    async def mock_llm_stream():
        chunks = ["你好！", "我是", "语嫣，", "很高兴", "为你服务。", "有什么", "可以帮助", "你的吗？"]
        for chunk in chunks:
            yield chunk
            await asyncio.sleep(0.1)  # 模拟LLM延迟
    
    # 处理流式响应
    session_id = "test_session_123"
    final_response = await realtime_tts.handle_llm_streaming_response(
        session_id, mock_llm_stream()
    )
    
    print(f"Final response: {final_response}")


if __name__ == "__main__":
    asyncio.run(example_usage())