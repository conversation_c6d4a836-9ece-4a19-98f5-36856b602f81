#!/usr/bin/env python3
"""
测试 DoubaoTTS 功能
测试豆包TTS的流式语音合成能力
"""

import asyncio
import sys
import os
import time
import yaml
import subprocess
import platform
import shutil
import random
import tempfile
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.tts.doubao_tts import DoubaoTTS
from logger import get_logger

# 初始化日志器
logger = get_logger('TTS_Test')

# 全局测试案例定义
STREAMING_TEST_CASES = [
    {
        "name": "短对话流式",
        "segments": [
            "你好！",
            "我是",
            "语嫣，很高兴为你服务。",
            "有什么可以帮助你的吗？"
        ]
    },
    {
        "name": "解释性内容流式",
        "segments": [
            "人工智能",
            "是一门研究、开发",
            "用于模拟、延伸和扩展",
            "人的智能的理论、方法、",
            "技术及应用系统的",
            "一门新的技术科学。"
        ]
    },
    {
        "name": "情感化回复流式",
        "segments": [
            "哎呀，",
            "听起来你遇到困难",
            "了呢！别担心，",
            "我们一起想办法解决。",
            "你能具体描述一下",
            "遇到的问题吗？"
        ]
    }
]

def estimate_llm_delay(text: str, tokens_per_second: float = 20.0) -> float:
    """
    估算LLM生成文本的延迟时间
    
    Args:
        text: 文本内容
        tokens_per_second: LLM生成速度 (tokens/s)，默认20
        
    Returns:
        估算的延迟时间(秒)
    """
    # 简单估算：中文约1字符=1token，英文约4字符=1token
    # 这里主要是中文，使用1:1比例
    estimated_tokens = len(text)
    delay = estimated_tokens / tokens_per_second
    
    # 添加一些随机性，模拟真实LLM的波动
    variation = random.uniform(0.8, 1.2)  # ±20%的随机波动
    return delay * variation


def play_audio(filename):
    """播放音频文件"""
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["afplay", filename], check=True)
        elif system == "Windows":
            subprocess.run(["start", filename], shell=True, check=True)
        elif system == "Linux":
            # 尝试不同的播放器
            players = ["aplay", "paplay", "mpg123", "ffplay"]
            for player in players:
                try:
                    subprocess.run([player, filename], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
            else:
                logger.warning(f"未找到适合的音频播放器，请手动播放: {filename}")
        else:
            logger.warning(f"不支持的操作系统: {system}，请手动播放: {filename}")
    except Exception as e:
        logger.warning(f"播放音频失败: {e}，请手动播放: {filename}")


class StreamingAudioPlayer:
    """流式音频播放器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.chunk_counter = 0
        self.player_process = None
        self.fifo_path = None
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
    
    def _get_streaming_player_command(self):
        """获取支持流式播放的播放器命令"""
        system = platform.system()
        
        if system == "Darwin":  # macOS
            # 使用 ffplay (需要安装 ffmpeg)
            return ["ffplay", "-nodisp", "-autoexit", "-"]
        elif system == "Linux":
            # 尝试不同的播放器
            players = [
                ["ffplay", "-nodisp", "-autoexit", "-"],
                ["mpv", "--no-video", "--"],
                ["mplayer", "-"],
            ]
            for cmd in players:
                try:
                    # 检查播放器是否可用
                    subprocess.run([cmd[0], "--help"], 
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL, 
                                 check=True)
                    return cmd
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
        elif system == "Windows":
            # Windows 上使用 ffplay
            return ["ffplay", "-nodisp", "-autoexit", "-"]
            
        return None
    
    async def start_streaming_playback(self):
        """开始流式播放"""
        cmd = self._get_streaming_player_command()
        if not cmd:
            logger.warning("未找到支持流式播放的播放器 (需要 ffplay, mpv 或 mplayer)")
            return False
            
        try:
            logger.info(f"启动流式播放器: {' '.join(cmd)}")
            self.player_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            logger.error(f"无法启动流式播放器: {e}")
            return False
    
    async def play_chunk(self, audio_chunk: bytes):
        """播放音频块"""
        if self.player_process and self.player_process.stdin:
            try:
                self.player_process.stdin.write(audio_chunk)
                self.player_process.stdin.flush()
                return True
            except Exception as e:
                logger.error(f"播放音频块失败: {e}")
                return False
        return False
    
    def stop_streaming_playback(self):
        """停止流式播放"""
        if self.player_process:
            try:
                # 检查进程是否还在运行
                if self.player_process.poll() is None:
                    if self.player_process.stdin and not self.player_process.stdin.closed:
                        # 关闭stdin，告诉播放器不会有更多数据
                        logger.info("🎵 关闭播放器输入流...")
                        self.player_process.stdin.close()
                    
                    # 等待播放器自然结束（播放完缓冲区中的音频）
                    logger.info("🎵 等待播放器播放完剩余音频...")
                    try:
                        self.player_process.wait(timeout=8)  # 增加到8秒
                        logger.info("🎵 流式播放器正常结束")
                    except subprocess.TimeoutExpired:
                        logger.warning("🎵 播放器超时，尝试优雅终止...")
                        self.player_process.terminate()
                        try:
                            self.player_process.wait(timeout=3)
                            logger.info("🎵 播放器已优雅终止")
                        except subprocess.TimeoutExpired:
                            logger.warning("🎵 强制杀死播放器进程")
                            self.player_process.kill()
                else:
                    logger.info("🎵 播放器进程已经结束")
                        
            except Exception as e:
                logger.error(f"停止播放器时出错: {e}")
            finally:
                self.player_process = None
    
    def cleanup(self):
        """清理资源"""
        self.stop_streaming_playback()
        try:
            shutil.rmtree(self.temp_dir)
        except Exception:
            pass


def load_config():
    """从config.yaml加载配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"无法加载配置文件: {e}")
        return None


def load_tts_config():
    """加载TTS配置"""
    config = load_config()
    if not config:
        return None
    
    # 从配置文件获取豆包TTS设置
    tts_config = config.get('tts', {})
    api_keys = config.get('api_key', {})
    doubao_tts_keys = api_keys.get('doubao_tts', {})
    
    return {
        'app_id': doubao_tts_keys.get('app_id'),
        'access_token': doubao_tts_keys.get('access_token'),
        'ws_url': tts_config.get('ws_url', 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'),
        'speaker': tts_config.get('speaker', 'zh_female_shuangkuaisisi_moon_bigtts'),
        'audio_format': tts_config.get('audio_format', 'mp3'),
        'sample_rate': tts_config.get('sample_rate', 24000),
        'output_dir': config.get('websocket_audio', {}).get('output_dir', 'log/audio')
    }


async def test_tts_streaming():
    """测试DoubaoTTS流式功能（模拟LLM流式输出）"""
    logger.info("测试豆包TTS流式功能（模拟LLM流式输出）")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        logger.error("在 api_key.doubao_tts 下设置 app_id 和 access_token")
        logger.error("可以从火山引擎控制台获取：https://console.volcengine.com/")
        return False
    
    try:
        # 使用全局定义的测试案例
        for test_case_idx, test_case in enumerate(STREAMING_TEST_CASES, 1):
            logger.info(f"测试 {test_case_idx}: {test_case['name']}")
            segments = test_case['segments']
            full_text = "".join(segments)
            logger.info(f"完整文本: {full_text}")
            logger.info(f"分段数量: {len(segments)} 段")
            
            # 使用独立的DoubaoTTS会话处理多个片段
            logger.info("DoubaoTTS流式处理 - 异步发送文本和接收音频:")
            start_time = time.time()
            
            all_chunks = []
            total_chunk_count = 0
            
            with StreamingAudioPlayer() as player:
                # 启动流式播放器
                if await player.start_streaming_playback():
                    logger.info("🎵 启动实时音频播放")
                else:
                    logger.warning("无法启动实时播放，将收集音频后播放")
                    player = None
                
                # 使用async with管理一个独立的TTS会话
                async with DoubaoTTS(config) as tts:
                    logger.info(f"🎯 TTS会话已启动 - 案例: {test_case['name']}")
                    
                    # 创建两个独立的任务：发送文本和接收音频
                    async def send_text_segments():
                        """模拟LLM流式输出，按节奏发送文本片段"""
                        for i, segment in enumerate(segments, 1):
                            logger.info(f"📤 LLM生成片段 {i}/{len(segments)}: '{segment}'")
                            
                            # 模拟LLM延迟
                            delay = estimate_llm_delay(segment)
                            logger.info(f"⏱️  模拟LLM延迟: {delay:.2f}秒")
                            await asyncio.sleep(delay)
                            
                            # 发送文本片段到TTS会话（不等待音频返回）
                            await tts.send_text_segment(segment)
                            logger.info(f"✅ 片段 {i} 已发送到TTS")
                        
                        logger.info("📤 所有文本片段发送完毕")
                        # 通知DoubaoTTS不会再有更多文本片段，可以完成音频生成
                        await tts._finish_session(tts.ws, tts.session_id)
                        logger.info("🏁 已通知TTS完成会话，等待剩余音频...")
                    
                    async def receive_audio_chunks():
                        """独立地接收音频流，有音频就播放"""
                        nonlocal all_chunks, total_chunk_count
                        
                        logger.info("🎵 开始接收音频流...")
                        async for audio_chunk in tts.get_audio_stream():
                            all_chunks.append(audio_chunk)
                            total_chunk_count += 1
                            
                            # 实时播放音频块
                            if player:
                                await player.play_chunk(audio_chunk)
                            
                            logger.info(f"🎵 收到音频块 {total_chunk_count}: {len(audio_chunk)} 字节")
                        
                        logger.info("🎵 音频流接收完毕")
                    
                    # 并发执行发送文本和接收音频两个任务
                    await asyncio.gather(
                        send_text_segments(),
                        receive_audio_chunks()
                    )
                    
                    # 给流式播放器一些时间播放完最后的音频块
                    if player and player.player_process:
                        logger.info("⏳ 等待流式播放器播放完最后的音频...")
                        await asyncio.sleep(3)  # 给播放器3秒时间播放完剩余音频
                
                logger.info("🎯 TTS会话已完成")
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 保存和播放完整音频（用于对比）
            if all_chunks:
                audio_filename = f"streaming_test_{test_case_idx}_{test_case['name']}.mp3"
                logger.info(f"💾 保存完整音频到: {audio_filename}")
                
                combined_audio = b''.join(all_chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                total_size = sum(len(chunk) for chunk in all_chunks)
                logger.info(f"⏱️  会话总耗时: {duration:.2f}秒")
                logger.info(f"📊 音频数据: {total_chunk_count} 块, 总计 {total_size} 字节")
                
                logger.info("🔊 对比: 播放完整音频文件")
                play_audio(audio_filename)
                input("🎵 播放完成，按回车键继续下一个测试...")
            else:
                logger.warning("❌ 未收到音频数据")
            
            logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tts_streaming_multi_session():
    """测试多个TTS会话并发处理（使用相同的流式测试案例）"""
    logger.info("🔄 测试豆包TTS多会话并发")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        logger.info(f"🚀 启动 {len(STREAMING_TEST_CASES)} 个并发TTS流式任务")
        
        async def process_streaming_tts(test_case, session_id):
            """处理单个流式TTS任务"""
            start_time = time.time()
            segments = test_case['segments']
            full_text = "".join(segments)
            
            logger.info(f"   🎵 会话{session_id}开始: {test_case['name']} - {full_text[:30]}...")
            
            all_chunks = []
            total_chunk_count = 0
            
            # 使用单独的DoubaoTTS会话处理流式音频
            async with DoubaoTTS(config) as tts:
                logger.info(f"   🎯 TTS会话{session_id}已启动")
                
                # 发送文本片段
                async def send_text_segments():
                    for i, segment in enumerate(segments, 1):
                        # 模拟LLM延迟
                        delay = estimate_llm_delay(segment) * 0.5  # 并发时缩短延迟
                        await asyncio.sleep(delay)
                        
                        await tts.send_text_segment(segment)
                        logger.info(f"   📤 会话{session_id} 片段{i}: '{segment}'")
                    
                    # 通知完成
                    await tts._finish_session(tts.ws, tts.session_id)
                    logger.info(f"   🏁 会话{session_id} 文本发送完毕")
                
                # 接收音频
                async def receive_audio_chunks():
                    nonlocal all_chunks, total_chunk_count
                    async for audio_chunk in tts.get_audio_stream():
                        all_chunks.append(audio_chunk)
                        total_chunk_count += 1
                
                # 并发执行
                await asyncio.gather(
                    send_text_segments(),
                    receive_audio_chunks()
                )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if all_chunks:
                total_size = sum(len(chunk) for chunk in all_chunks)
                audio_filename = f"concurrent_streaming_{session_id}_{test_case['name']}.mp3"
                
                combined_audio = b''.join(all_chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                logger.info(f"   ✅ 会话{session_id}完成: {duration:.2f}秒, {total_chunk_count}块, {total_size}字节")
                return True, audio_filename, duration, total_chunk_count, total_size, test_case['name']
            else:
                logger.error(f"   会话{session_id}失败: 未收到音频数据")
                return False, None, duration, 0, 0, test_case['name']
        
        # 并发执行所有TTS任务
        tasks = [
            process_streaming_tts(test_case, i+1) 
            for i, test_case in enumerate(STREAMING_TEST_CASES)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        successful_count = 0
        total_duration = 0
        total_chunks = 0
        total_bytes = 0
        audio_files = []
        
        logger.info("📊 并发流式测试结果:")
        for i, result in enumerate(results, 1):
            if isinstance(result, Exception):
                logger.error(f"   会话{i}: 异常 - {result}")
            else:
                success, filename, duration, chunks, bytes_count, case_name = result
                if success:
                    successful_count += 1
                    total_duration += duration
                    total_chunks += chunks
                    total_bytes += bytes_count
                    if filename:
                        audio_files.append((filename, case_name))
                    logger.info(f"   ✅ 会话{i} ({case_name}): {duration:.2f}秒, {chunks}块, {bytes_count}字节")
                else:
                    logger.error(f"   会话{i} ({case_name}): 失败")
        
        logger.info(f"🎯 总体统计:")
        logger.info(f"   成功率: {successful_count}/{len(STREAMING_TEST_CASES)} ({successful_count/len(STREAMING_TEST_CASES)*100:.1f}%)")
        logger.info(f"   平均耗时: {total_duration/max(successful_count,1):.2f}秒")
        logger.info(f"   总音频块: {total_chunks}")
        logger.info(f"   总字节数: {total_bytes}")
        
        # 依次播放所有生成的音频，检查是否有混乱
        if audio_files:
            logger.info(f"🔊 依次播放 {len(audio_files)} 个并发生成的音频文件:")
            logger.info("🎯 请仔细听音频内容是否正确，检查是否有语音混乱")
            
            for i, (filename, case_name) in enumerate(audio_files, 1):
                logger.info(f"   播放文件{i}: {case_name} - {filename}")
                play_audio(filename)
                if i < len(audio_files):  # 不是最后一个文件
                    input(f"   按回车继续播放下一个音频...")
            
            input("🎵 所有并发音频播放完成，按回车键继续...")
        
        return successful_count == len(STREAMING_TEST_CASES)
        
    except Exception as e:
        logger.error(f"并发流式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("🎵 豆包TTS测试套件")
    logger.info("=" * 60)
    
    # 检查输出目录
    output_dir = Path("log/audio")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    test_results = []
    
    # 测试流式TTS（模拟LLM流式输出）
    result1 = await test_tts_streaming()
    test_results.append(("流式TTS", result1))
    
    # 多会话并发测试
    result2 = await test_tts_streaming_multi_session()
    test_results.append(("多会话并发", result2))
    
    # 总结
    logger.info("📋 测试结果总结")
    logger.info("=" * 30)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    logger.info(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！豆包TTS功能正常")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和网络连接")
    
    logger.info("💡 注意事项:")
    logger.info("   1. 确保在 config/config.yaml 中正确配置豆包TTS凭证")
    logger.info("      在 api_key.doubao_tts 下设置 app_id 和 access_token")
    logger.info("   2. 确保网络连接正常，能够访问豆包TTS服务")
    logger.info("   3. 生成的音频文件保存在log/audio目录下")
    logger.info("   4. 实时流式播放需要安装 ffmpeg (brew install ffmpeg / apt install ffmpeg)")
    logger.info("      或安装 mpv/mplayer 等支持流式播放的播放器")
    logger.info("📝 配置示例:")
    logger.info("api_key:")
    logger.info("  doubao_tts:")
    logger.info("    app_id: your-app-id")
    logger.info("    access_token: \"your-access-token\"")
    logger.info("tts:")
    logger.info("  provider: \"doubao\"")
    logger.info("  speaker: \"zh_female_shuangkuaisisi_moon_bigtts\"")
    logger.info("  audio_format: \"mp3\"")
    logger.info("  sample_rate: 24000")


if __name__ == "__main__":
    asyncio.run(main())