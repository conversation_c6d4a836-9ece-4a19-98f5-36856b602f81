#!/usr/bin/env python3
"""
测试 DoubaoTTS 功能
测试豆包TTS的流式语音合成能力
"""

import asyncio
import sys
import os
import time
import yaml
import subprocess
import platform
import shutil
import random
import tempfile
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.tts.doubao_tts import DoubaoTTS, DoubaoTTSSession
from logger import get_logger

# 初始化日志器
logger = get_logger('TTS_Test')

def estimate_llm_delay(text: str, tokens_per_second: float = 20.0) -> float:
    """
    估算LLM生成文本的延迟时间
    
    Args:
        text: 文本内容
        tokens_per_second: LLM生成速度 (tokens/s)，默认20
        
    Returns:
        估算的延迟时间(秒)
    """
    # 简单估算：中文约1字符=1token，英文约4字符=1token
    # 这里主要是中文，使用1:1比例
    estimated_tokens = len(text)
    delay = estimated_tokens / tokens_per_second
    
    # 添加一些随机性，模拟真实LLM的波动
    variation = random.uniform(0.8, 1.2)  # ±20%的随机波动
    return delay * variation


def play_audio(filename):
    """播放音频文件"""
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["afplay", filename], check=True)
        elif system == "Windows":
            subprocess.run(["start", filename], shell=True, check=True)
        elif system == "Linux":
            # 尝试不同的播放器
            players = ["aplay", "paplay", "mpg123", "ffplay"]
            for player in players:
                try:
                    subprocess.run([player, filename], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
            else:
                logger.warning(f"未找到适合的音频播放器，请手动播放: {filename}")
        else:
            logger.warning(f"不支持的操作系统: {system}，请手动播放: {filename}")
    except Exception as e:
        logger.warning(f"播放音频失败: {e}，请手动播放: {filename}")


class StreamingAudioPlayer:
    """流式音频播放器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.chunk_counter = 0
        self.player_process = None
        self.fifo_path = None
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
    
    def _get_streaming_player_command(self):
        """获取支持流式播放的播放器命令"""
        system = platform.system()
        
        if system == "Darwin":  # macOS
            # 使用 ffplay (需要安装 ffmpeg)
            return ["ffplay", "-nodisp", "-autoexit", "-"]
        elif system == "Linux":
            # 尝试不同的播放器
            players = [
                ["ffplay", "-nodisp", "-autoexit", "-"],
                ["mpv", "--no-video", "--"],
                ["mplayer", "-"],
            ]
            for cmd in players:
                try:
                    # 检查播放器是否可用
                    subprocess.run([cmd[0], "--help"], 
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL, 
                                 check=True)
                    return cmd
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
        elif system == "Windows":
            # Windows 上使用 ffplay
            return ["ffplay", "-nodisp", "-autoexit", "-"]
            
        return None
    
    async def start_streaming_playback(self):
        """开始流式播放"""
        cmd = self._get_streaming_player_command()
        if not cmd:
            logger.warning("未找到支持流式播放的播放器 (需要 ffplay, mpv 或 mplayer)")
            return False
            
        try:
            logger.info(f"启动流式播放器: {' '.join(cmd)}")
            self.player_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            logger.error(f"无法启动流式播放器: {e}")
            return False
    
    async def play_chunk(self, audio_chunk: bytes):
        """播放音频块"""
        if self.player_process and self.player_process.stdin:
            try:
                self.player_process.stdin.write(audio_chunk)
                self.player_process.stdin.flush()
                return True
            except Exception as e:
                logger.error(f"播放音频块失败: {e}")
                return False
        return False
    
    def stop_streaming_playback(self):
        """停止流式播放"""
        if self.player_process:
            try:
                if self.player_process.stdin:
                    self.player_process.stdin.close()
                self.player_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.player_process.kill()
            except Exception as e:
                logger.error(f"停止播放器时出错: {e}")
            finally:
                self.player_process = None
    
    def cleanup(self):
        """清理资源"""
        self.stop_streaming_playback()
        try:
            shutil.rmtree(self.temp_dir)
        except Exception:
            pass


def load_config():
    """从config.yaml加载配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"无法加载配置文件: {e}")
        return None


def load_tts_config():
    """加载TTS配置"""
    config = load_config()
    if not config:
        return None
    
    # 从配置文件获取豆包TTS设置
    tts_config = config.get('tts', {})
    api_keys = config.get('api_key', {})
    doubao_tts_keys = api_keys.get('doubao_tts', {})
    
    return {
        'app_id': doubao_tts_keys.get('app_id'),
        'access_token': doubao_tts_keys.get('access_token'),
        'ws_url': tts_config.get('ws_url', 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'),
        'speaker': tts_config.get('speaker', 'zh_female_shuangkuaisisi_moon_bigtts'),
        'audio_format': tts_config.get('audio_format', 'mp3'),
        'sample_rate': tts_config.get('sample_rate', 24000),
        'output_dir': config.get('websocket_audio', {}).get('output_dir', 'log/audio')
    }


async def test_tts_streaming():
    """测试DoubaoTTS流式功能（模拟LLM流式输出）"""
    logger.info("测试豆包TTS流式功能（模拟LLM流式输出）")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        logger.error("在 api_key.doubao_tts 下设置 app_id 和 access_token")
        logger.error("可以从火山引擎控制台获取：https://console.volcengine.com/")
        return False
    
    try:
        # 模拟LLM流式输出的测试案例 - 现在每个文本片段单独处理
        streaming_test_cases = [
            {
                "name": "短对话流式",
                "segments": [
                    "你好！",
                    "我是",
                    "语嫣，很高兴为你服务。",
                    "有什么可以帮助你的吗？"
                ]
            },
            {
                "name": "解释性内容流式",
                "segments": [
                    "人工智能",
                    "是一门研究、开发",
                    "用于模拟、延伸和扩展",
                    "人的智能的理论、方法、",
                    "技术及应用系统的",
                    "一门新的技术科学。"
                ]
            },
            {
                "name": "情感化回复流式",
                "segments": [
                    "哎呀，",
                    "听起来你遇到困难",
                    "了呢！别担心，",
                    "我们一起想办法解决。",
                    "你能具体描述一下",
                    "遇到的问题吗？"
                ]
            }
        ]
        
        for test_case_idx, test_case in enumerate(streaming_test_cases, 1):
            logger.info(f"测试 {test_case_idx}: {test_case['name']}")
            segments = test_case['segments']
            full_text = "".join(segments)
            logger.info(f"完整文本: {full_text}")
            logger.info(f"分段数量: {len(segments)} 段")
            
            # 使用DoubaoTTS流式处理 - 逐个片段处理
            logger.info("DoubaoTTS流式处理 - 逐个片段:")
            start_time = time.time()
            
            all_chunks = []
            total_chunk_count = 0
            
            for i, segment in enumerate(segments, 1):
                logger.info(f"处理片段 {i}/{len(segments)}: '{segment}'")
                
                # 模拟LLM延迟
                delay = estimate_llm_delay(segment)
                logger.info(f"模拟LLM延迟: {delay:.2f}秒")
                await asyncio.sleep(delay)
                
                # 立即发送给TTS
                tts = DoubaoTTS(config)
                segment_chunks = []
                
                async for audio_chunk in tts.text_to_speech_stream(segment):
                    segment_chunks.append(audio_chunk)
                    all_chunks.append(audio_chunk)
                    total_chunk_count += 1
                    logger.info(f"收到片段{i}音频块: {len(audio_chunk)} 字节")
                
                logger.info(f"片段{i}完成: {len(segment_chunks)} 个音频块")
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 保存和播放音频
            if all_chunks:
                audio_filename = f"streaming_test_{test_case_idx}_{test_case['name']}.mp3"
                logger.info(f"保存音频到: {audio_filename}")
                
                combined_audio = b''.join(all_chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                total_size = sum(len(chunk) for chunk in all_chunks)
                logger.info(f"TTS完成: {duration:.2f}秒")
                logger.info(f"音频数据: {total_chunk_count} 块, 总计 {total_size} 字节")
                logger.info(f"播放音频: {audio_filename}")
                
                play_audio(audio_filename)
                input("🎵 播放完成，按回车键继续下一个测试...")
            else:
                logger.warning("未收到音频数据")
            
            logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tts_session_management():
    """测试TTS会话管理功能（多片段在一个会话中）"""
    logger.info("🔄 测试豆包TTS会话管理功能")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        # 测试会话管理：多个文本片段在一个会话中处理
        test_segments = [
            "你好！",
            "我是语嫣，",
            "很高兴为你服务。",
            "今天我们来测试",
            "多片段会话管理功能。"
        ]
        
        logger.info(f"📝 测试文本片段: {test_segments}")
        logger.info(f"📊 总计 {len(test_segments)} 个片段")
        
        start_time = time.time()
        all_audio_chunks = []
        
        # 使用DoubaoTTSSession进行会话管理
        async with DoubaoTTSSession(config, "session_test") as session:
            logger.info("🎯 TTS会话已启动")
            
            for i, segment in enumerate(test_segments, 1):
                logger.info(f"📤 发送片段 {i}/{len(test_segments)}: '{segment}'")
                
                # 发送文本片段到同一个会话
                success = await session.send_text_segment(segment)
                if not success:
                    logger.error(f"❌ 发送片段 {i} 失败")
                    return False
                
                logger.info(f"✅ 片段 {i} 发送成功")
                
                # 获取这个片段的音频流
                segment_chunks = []
                async for audio_chunk in session.get_audio_stream():
                    segment_chunks.append(audio_chunk)
                    all_audio_chunks.append(audio_chunk)
                
                logger.info(f"🎵 片段 {i} 音频接收完成: {len(segment_chunks)} 个音频块")
                
                # 模拟真实场景中的处理延迟
                await asyncio.sleep(0.1)
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info("🎯 TTS会话已完成")
        
        # 保存和播放完整音频
        if all_audio_chunks:
            audio_filename = "session_management_test.mp3"
            logger.info(f"💾 保存完整音频到: {audio_filename}")
            
            combined_audio = b''.join(all_audio_chunks)
            with open(audio_filename, 'wb') as f:
                f.write(combined_audio)
            
            total_size = sum(len(chunk) for chunk in all_audio_chunks)
            logger.info(f"⏱️  会话总耗时: {duration:.2f}秒")
            logger.info(f"📊 音频数据: {len(all_audio_chunks)} 块, 总计 {total_size} 字节")
            logger.info(f"🔊 播放音频: {audio_filename}")
            
            play_audio(audio_filename)
            input("🎵 播放完成，按回车键继续...")
            
            return True
        else:
            logger.error("❌ 未收到任何音频数据")
            return False
            
    except Exception as e:
        logger.error(f"会话管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tts_streaming_multi_session():
    """测试多个TTS会话并发处理"""
    logger.info("🔄 测试豆包TTS多会话并发")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        # 模拟真实场景中的并发TTS请求
        concurrent_texts = [
            "第一个用户问：今天天气怎么样？",
            "第二个用户说：请帮我解释一下人工智能。",
            "第三个用户问：你能推荐一些好吃的餐厅吗？",
            "第四个用户说：我想了解一下最新的科技发展。"
        ]
        
        logger.info(f"🚀 启动 {len(concurrent_texts)} 个并发TTS任务")
        
        async def process_single_tts(text, session_id):
            """处理单个TTS任务"""
            tts = DoubaoTTS(config)
            chunks = []
            start_time = time.time()
            
            logger.info(f"   🎵 会话{session_id}开始: {text[:30]}...")
            
            async for audio_chunk in tts.text_to_speech_stream(text, f"session_{session_id}"):
                chunks.append(audio_chunk)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if chunks:
                total_size = sum(len(chunk) for chunk in chunks)
                audio_filename = f"concurrent_session_{session_id}.mp3"
                
                combined_audio = b''.join(chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                logger.info(f"   ✅ 会话{session_id}完成: {duration:.2f}秒, {len(chunks)}块, {total_size}字节")
                return True, audio_filename, duration, len(chunks), total_size
            else:
                logger.error(f"   会话{session_id}失败: 未收到音频数据")
                return False, None, duration, 0, 0
        
        # 并发执行所有TTS任务
        tasks = [
            process_single_tts(text, i+1) 
            for i, text in enumerate(concurrent_texts)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        successful_count = 0
        total_duration = 0
        total_chunks = 0
        total_bytes = 0
        audio_files = []
        
        logger.info("📊 并发测试结果:")
        for i, result in enumerate(results, 1):
            if isinstance(result, Exception):
                logger.error(f"   会话{i}: 异常 - {result}")
            else:
                success, filename, duration, chunks, bytes_count = result
                if success:
                    successful_count += 1
                    total_duration += duration
                    total_chunks += chunks
                    total_bytes += bytes_count
                    if filename:
                        audio_files.append(filename)
                    logger.info(f"   ✅ 会话{i}: {duration:.2f}秒, {chunks}块, {bytes_count}字节")
                else:
                    logger.error(f"   会话{i}: 失败")
        
        logger.info(f"🎯 总体统计:")
        logger.info(f"   成功率: {successful_count}/{len(concurrent_texts)} ({successful_count/len(concurrent_texts)*100:.1f}%)")
        logger.info(f"   平均耗时: {total_duration/max(successful_count,1):.2f}秒")
        logger.info(f"   总音频块: {total_chunks}")
        logger.info(f"   总字节数: {total_bytes}")
        
        # 播放所有生成的音频
        if audio_files:
            logger.info(f"🔊 依次播放 {len(audio_files)} 个音频文件:")
            for i, filename in enumerate(audio_files, 1):
                logger.info(f"   播放文件{i}: {filename}")
                play_audio(filename)
                if i < len(audio_files):  # 不是最后一个文件
                    input(f"   按回车继续播放下一个音频...")
            
            input("🎵 所有音频播放完成，按回车键继续...")
        
        return successful_count == len(concurrent_texts)
        
    except Exception as e:
        logger.error(f"并发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("🎵 豆包TTS测试套件")
    logger.info("=" * 60)
    
    # 检查输出目录
    output_dir = Path("log/audio")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    test_results = []
    
    # 测试流式TTS（模拟LLM流式输出）
    result1 = await test_tts_streaming()
    test_results.append(("流式TTS", result1))
    
    # 会话管理测试
    result4 = await test_tts_session_management()
    test_results.append(("会话管理", result4))
    
    # 多会话并发测试
    result5 = await test_tts_streaming_multi_session()
    test_results.append(("多会话并发", result5))
    
    # 总结
    logger.info("📋 测试结果总结")
    logger.info("=" * 30)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    logger.info(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！豆包TTS功能正常")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和网络连接")
    
    logger.info("💡 注意事项:")
    logger.info("   1. 确保在 config/config.yaml 中正确配置豆包TTS凭证")
    logger.info("      在 api_key.doubao_tts 下设置 app_id 和 access_token")
    logger.info("   2. 确保网络连接正常，能够访问豆包TTS服务")
    logger.info("   3. 生成的音频文件保存在log/audio目录下")
    logger.info("   4. 实时流式播放需要安装 ffmpeg (brew install ffmpeg / apt install ffmpeg)")
    logger.info("      或安装 mpv/mplayer 等支持流式播放的播放器")
    logger.info("📝 配置示例:")
    logger.info("api_key:")
    logger.info("  doubao_tts:")
    logger.info("    app_id: your-app-id")
    logger.info("    access_token: \"your-access-token\"")
    logger.info("tts:")
    logger.info("  provider: \"doubao\"")
    logger.info("  speaker: \"zh_female_shuangkuaisisi_moon_bigtts\"")
    logger.info("  audio_format: \"mp3\"")
    logger.info("  sample_rate: 24000")


if __name__ == "__main__":
    asyncio.run(main())