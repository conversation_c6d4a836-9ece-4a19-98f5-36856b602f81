#!/usr/bin/env python3
"""
测试 DoubaoTTS 功能
测试豆包TTS的流式语音合成能力
"""

import asyncio
import sys
import os
import time
import yaml
import subprocess
import platform
import shutil
import random
import tempfile
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.tts.doubao_tts import DoubaoTTS
from logger import get_logger

# 初始化日志器
logger = get_logger('TTS_Test')

def estimate_llm_delay(text: str, tokens_per_second: float = 20.0) -> float:
    """
    估算LLM生成文本的延迟时间
    
    Args:
        text: 文本内容
        tokens_per_second: LLM生成速度 (tokens/s)，默认20
        
    Returns:
        估算的延迟时间(秒)
    """
    # 简单估算：中文约1字符=1token，英文约4字符=1token
    # 这里主要是中文，使用1:1比例
    estimated_tokens = len(text)
    delay = estimated_tokens / tokens_per_second
    
    # 添加一些随机性，模拟真实LLM的波动
    variation = random.uniform(0.8, 1.2)  # ±20%的随机波动
    return delay * variation


def play_audio(filename):
    """播放音频文件"""
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["afplay", filename], check=True)
        elif system == "Windows":
            subprocess.run(["start", filename], shell=True, check=True)
        elif system == "Linux":
            # 尝试不同的播放器
            players = ["aplay", "paplay", "mpg123", "ffplay"]
            for player in players:
                try:
                    subprocess.run([player, filename], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
            else:
                logger.warning(f"未找到适合的音频播放器，请手动播放: {filename}")
        else:
            logger.warning(f"不支持的操作系统: {system}，请手动播放: {filename}")
    except Exception as e:
        logger.warning(f"播放音频失败: {e}，请手动播放: {filename}")


class StreamingAudioPlayer:
    """流式音频播放器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.chunk_counter = 0
        self.player_process = None
        self.fifo_path = None
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
    
    def _get_streaming_player_command(self):
        """获取支持流式播放的播放器命令"""
        system = platform.system()
        
        if system == "Darwin":  # macOS
            # 使用 ffplay (需要安装 ffmpeg)
            return ["ffplay", "-nodisp", "-autoexit", "-"]
        elif system == "Linux":
            # 尝试不同的播放器
            players = [
                ["ffplay", "-nodisp", "-autoexit", "-"],
                ["mpv", "--no-video", "--"],
                ["mplayer", "-"],
            ]
            for cmd in players:
                try:
                    # 检查播放器是否可用
                    subprocess.run([cmd[0], "--help"], 
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL, 
                                 check=True)
                    return cmd
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
        elif system == "Windows":
            # Windows 上使用 ffplay
            return ["ffplay", "-nodisp", "-autoexit", "-"]
            
        return None
    
    async def start_streaming_playback(self):
        """开始流式播放"""
        cmd = self._get_streaming_player_command()
        if not cmd:
            logger.warning("未找到支持流式播放的播放器 (需要 ffplay, mpv 或 mplayer)")
            return False
            
        try:
            logger.info(f"启动流式播放器: {' '.join(cmd)}")
            self.player_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            logger.error(f"无法启动流式播放器: {e}")
            return False
    
    async def play_chunk(self, audio_chunk: bytes):
        """播放音频块"""
        if self.player_process and self.player_process.stdin:
            try:
                self.player_process.stdin.write(audio_chunk)
                self.player_process.stdin.flush()
                return True
            except Exception as e:
                logger.error(f"播放音频块失败: {e}")
                return False
        return False
    
    def stop_streaming_playback(self):
        """停止流式播放"""
        if self.player_process:
            try:
                if self.player_process.stdin:
                    self.player_process.stdin.close()
                self.player_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.player_process.kill()
            except Exception as e:
                logger.error(f"停止播放器时出错: {e}")
            finally:
                self.player_process = None
    
    def cleanup(self):
        """清理资源"""
        self.stop_streaming_playback()
        try:
            shutil.rmtree(self.temp_dir)
        except Exception:
            pass


def load_config():
    """从config.yaml加载配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"无法加载配置文件: {e}")
        return None


def load_tts_config():
    """加载TTS配置"""
    config = load_config()
    if not config:
        return None
    
    # 从配置文件获取豆包TTS设置
    tts_config = config.get('tts', {})
    api_keys = config.get('api_key', {})
    doubao_tts_keys = api_keys.get('doubao_tts', {})
    
    return {
        'app_id': doubao_tts_keys.get('app_id'),
        'access_token': doubao_tts_keys.get('access_token'),
        'ws_url': tts_config.get('ws_url', 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'),
        'speaker': tts_config.get('speaker', 'zh_female_shuangkuaisisi_moon_bigtts'),
        'audio_format': tts_config.get('audio_format', 'mp3'),
        'sample_rate': tts_config.get('sample_rate', 24000),
        'output_dir': config.get('websocket_audio', {}).get('output_dir', 'log/audio')
    }


async def test_tts_streaming_with_realtime_playback():
    """测试DoubaoTTS流式功能并实时播放（真正的流式体验）"""
    logger.info("测试豆包TTS流式功能并实时播放")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        logger.error("在 api_key.doubao_tts 下设置 app_id 和 access_token")
        logger.error("可以从火山引擎控制台获取：https://console.volcengine.com/")
        return False
    
    # 选择一个测试案例
    test_case = {
        "name": "实时播放测试",
        "segments": [
            "你好！",
            "我是语嫣，",
            "很高兴为你服务。",
            "今天我们来体验一下",
            "真正的流式语音播放功能。",
            "你现在听到的声音",
            "是实时生成和播放的。"
        ]
    }
    
    logger.info(f"测试案例: {test_case['name']}")
    segments = test_case['segments']
    full_text = "".join(segments)
    logger.info(f"完整文本: {full_text}")
    logger.info(f"分段数量: {len(segments)} 段")
    
    try:
        with StreamingAudioPlayer() as player:
            # 启动流式播放器
            if not await player.start_streaming_playback():
                logger.warning("无法启动流式播放，回退到传统播放方式")
                return await test_tts_streaming()
            
            logger.info("开始流式TTS和实时播放")
            start_time = time.time()
            
            tts = DoubaoTTS(config)
            chunks_for_file = []
            chunk_count = 0
            first_audio_time = None
            
            async for audio_chunk in tts.text_to_speech_stream(segments):
                chunk_count += 1
                chunks_for_file.append(audio_chunk)
                
                # 记录首次音频到达时间
                if first_audio_time is None:
                    first_audio_time = time.time()
                    logger.info(f"首块音频延迟: {first_audio_time - start_time:.2f}秒")
                
                # 实时播放音频块
                success = await player.play_chunk(audio_chunk)
                if success:
                    logger.info(f"播放音频块 {chunk_count}: {len(audio_chunk)} 字节")
                else:
                    logger.error(f"播放音频块 {chunk_count} 失败: {len(audio_chunk)} 字节")
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 等待播放完成
            logger.info("等待播放完成...")
            await asyncio.sleep(2)  # 给播放器一些时间完成播放
            
            logger.info(f"流式播放完成: {duration:.2f}秒")
            logger.info(f"音频数据: {chunk_count} 块, 总计 {sum(len(c) for c in chunks_for_file)} 字节")
            
            # 保存完整音频文件供对比
            if chunks_for_file:
                audio_filename = f"realtime_streaming_test.mp3"
                logger.info(f"保存完整音频到: {audio_filename}")
                
                combined_audio = b''.join(chunks_for_file)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                logger.info("对比: 播放完整音频文件")
                play_audio(audio_filename)
                
            input("🎵 测试完成，按回车键继续...")
            
        return True
        
    except Exception as e:
        logger.error(f"流式播放测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tts_streaming():
    """测试DoubaoTTS流式功能（模拟LLM流式输出）"""
    logger.info("测试豆包TTS流式功能（模拟LLM流式输出）")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        logger.error("在 api_key.doubao_tts 下设置 app_id 和 access_token")
        logger.error("可以从火山引擎控制台获取：https://console.volcengine.com/")
        return False
    
    try:
        # 模拟LLM流式输出的测试案例
        streaming_test_cases = [
            {
                "name": "短对话流式",
                "segments": [
                    "你好！",
                    "我是语嫣，",
                    "很高兴为你服务。",
                    "有什么可以帮助你的吗？"
                ]
            },
            {
                "name": "解释性内容流式",
                "segments": [
                    "人工智能",
                    "是一门研究、开发",
                    "用于模拟、延伸和扩展",
                    "人的智能的理论、方法、",
                    "技术及应用系统的",
                    "一门新的技术科学。"
                ]
            },
            {
                "name": "情感化回复流式",
                "segments": [
                    "哎呀，",
                    "听起来你遇到困难了呢！",
                    "别担心，",
                    "我们一起想办法解决。",
                    "你能具体描述一下",
                    "遇到的问题吗？"
                ]
            }
        ]
        
        for test_case_idx, test_case in enumerate(streaming_test_cases, 1):
            logger.info(f"测试 {test_case_idx}: {test_case['name']}")
            segments = test_case['segments']
            full_text = "".join(segments)
            logger.info(f"完整文本: {full_text}")
            logger.info(f"分段数量: {len(segments)} 段")
            
            # 模拟LLM流式输出过程
            logger.info("模拟LLM流式输出过程:")
            await simulate_llm_streaming(segments)
            
            # 使用DoubaoTTS流式处理
            logger.info("DoubaoTTS流式处理:")
            start_time = time.time()
            
            tts = DoubaoTTS(config)
            chunks = []
            chunk_count = 0
            
            async for audio_chunk in tts.text_to_speech_stream(segments):
                chunks.append(audio_chunk)
                chunk_count += 1
                logger.info(f"收到音频块 {chunk_count}: {len(audio_chunk)} 字节")
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 保存和播放音频
            if chunks:
                audio_filename = f"streaming_test_{test_case_idx}_{test_case['name']}.mp3"
                logger.info(f"保存音频到: {audio_filename}")
                
                combined_audio = b''.join(chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                total_size = sum(len(chunk) for chunk in chunks)
                logger.info(f"TTS完成: {duration:.2f}秒")
                logger.info(f"音频数据: {chunk_count} 块, 总计 {total_size} 字节")
                logger.info(f"播放音频: {audio_filename}")
                
                play_audio(audio_filename)
                input("🎵 播放完成，按回车键继续下一个测试...")
            else:
                logger.warning("未收到音频数据")
            
            logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def simulate_llm_streaming(segments, tokens_per_second: float = 20.0):
    """模拟LLM流式输出过程，基于真实的token生成速度"""
    accumulated_text = ""
    
    for i, segment in enumerate(segments, 1):
        # 基于LLM生成速度计算延迟
        delay = estimate_llm_delay(segment, tokens_per_second)
        logger.info(f"LLM生成段{i}: 预计生成'{segment}'需要{delay:.2f}秒 (约{len(segment)}tokens @ {tokens_per_second}tok/s)")
        
        await asyncio.sleep(delay)
        
        accumulated_text += segment
        logger.info(f"LLM输出段{i}: '{segment}' | 累积文本: '{accumulated_text}'")
    
    logger.info(f"LLM生成完成，最终文本: '{accumulated_text}'")


async def test_tts_file_output():
    """测试TTS文件输出功能"""
    logger.info("测试TTS文件输出功能")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        tts = DoubaoTTS(config)
        
        test_text = "这是一个TTS文件输出测试，我们将把这段话保存为音频文件。"
        output_path = "log/audio/test_tts_output.mp3"
        
        logger.info(f"测试文本: {test_text}")
        logger.info(f"输出路径: {output_path}")
        
        start_time = time.time()
        success = await tts.text_to_speech_file([test_text], output_path)
        end_time = time.time()
        
        if success:
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            logger.info(f"TTS文件生成成功: {end_time - start_time:.2f}秒")
            logger.info(f"文件大小: {file_size} 字节")
            logger.info(f"文件位置: {os.path.abspath(output_path)}")
            
            # 复制到当前目录并播放
            current_dir_file = "test_file_output.mp3"
            shutil.copy2(output_path, current_dir_file)
            logger.info(f"复制到当前目录: {current_dir_file}")
            
            logger.info(f"播放音频: {current_dir_file}")
            play_audio(current_dir_file)
            
            input("🎵 播放完成，按回车键继续...")
        else:
            logger.error("TTS文件生成失败")
        
        return success
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


async def test_tts_performance():
    """测试TTS性能"""
    logger.info("⚡ TTS性能测试")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        tts = DoubaoTTS(config)
        
        # 不同长度的文本测试
        test_cases = [
            ("短文本", "你好"),
            ("中等文本", "今天是一个美好的日子，阳光明媚，微风轻拂。"),
            ("长文本", "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。")
        ]
        
        performance_results = []
        
        for test_name, text in test_cases:
            logger.info(f"🔬 {test_name}测试: {len(text)} 字符")
            
            start_time = time.time()
            first_chunk_time = None
            chunk_count = 0
            total_size = 0
            
            chunks = []
            async for audio_chunk in tts.text_to_speech_stream([text]):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
                chunk_count += 1
                total_size += len(audio_chunk)
                chunks.append(audio_chunk)
            
            end_time = time.time()
            
            total_duration = end_time - start_time
            first_chunk_delay = (first_chunk_time - start_time) if first_chunk_time else total_duration
            
            result = {
                'name': test_name,
                'text_length': len(text),
                'total_duration': total_duration,
                'first_chunk_delay': first_chunk_delay,
                'chunk_count': chunk_count,
                'total_size': total_size,
                'chars_per_second': len(text) / total_duration if total_duration > 0 else 0
            }
            
            performance_results.append(result)
            
            logger.info(f"   ⏱️  总耗时: {total_duration:.2f}秒")
            logger.info(f"   ⚡ 首块延迟: {first_chunk_delay:.2f}秒")
            logger.info(f"   📦 音频块数: {chunk_count}")
            logger.info(f"   📊 音频大小: {total_size} 字节")
            logger.info(f"   🚀 处理速度: {result['chars_per_second']:.1f} 字符/秒")
            
            # 保存并播放音频
            if chunks:
                audio_filename = f"performance_test_{test_name}.mp3"
                logger.info(f"   💾 保存音频到: {audio_filename}")
                
                combined_audio = b''.join(chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                logger.info(f"   🔊 播放音频: {audio_filename}")
                play_audio(audio_filename)
                
                input("🎵 播放完成，按回车键继续下一个测试...")
        
        # 性能总结
        logger.info("📈 性能总结:")
        for result in performance_results:
            logger.info(f"   {result['name']}: {result['chars_per_second']:.1f} 字符/秒, 首块延迟 {result['first_chunk_delay']:.2f}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        return False



async def test_tts_streaming_multi_session():
    """测试多个TTS会话并发处理"""
    logger.info("🔄 测试豆包TTS多会话并发")
    logger.info("=" * 50)
    
    config = load_tts_config()
    
    if not config:
        logger.error("无法加载TTS配置")
        return False
    
    if not config.get('app_id') or not config.get('access_token'):
        logger.error("请在 config/config.yaml 中配置正确的豆包TTS凭证")
        return False
    
    try:
        # 模拟真实场景中的并发TTS请求
        concurrent_texts = [
            "第一个用户问：今天天气怎么样？",
            "第二个用户说：请帮我解释一下人工智能。",
            "第三个用户问：你能推荐一些好吃的餐厅吗？",
            "第四个用户说：我想了解一下最新的科技发展。"
        ]
        
        logger.info(f"🚀 启动 {len(concurrent_texts)} 个并发TTS任务")
        
        async def process_single_tts(text, session_id):
            """处理单个TTS任务"""
            tts = DoubaoTTS(config)
            chunks = []
            start_time = time.time()
            
            logger.info(f"   🎵 会话{session_id}开始: {text[:30]}...")
            
            async for audio_chunk in tts.text_to_speech_stream([text], f"session_{session_id}"):
                chunks.append(audio_chunk)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if chunks:
                total_size = sum(len(chunk) for chunk in chunks)
                audio_filename = f"concurrent_session_{session_id}.mp3"
                
                combined_audio = b''.join(chunks)
                with open(audio_filename, 'wb') as f:
                    f.write(combined_audio)
                
                logger.info(f"   ✅ 会话{session_id}完成: {duration:.2f}秒, {len(chunks)}块, {total_size}字节")
                return True, audio_filename, duration, len(chunks), total_size
            else:
                logger.error(f"   会话{session_id}失败: 未收到音频数据")
                return False, None, duration, 0, 0
        
        # 并发执行所有TTS任务
        tasks = [
            process_single_tts(text, i+1) 
            for i, text in enumerate(concurrent_texts)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        successful_count = 0
        total_duration = 0
        total_chunks = 0
        total_bytes = 0
        audio_files = []
        
        logger.info("📊 并发测试结果:")
        for i, result in enumerate(results, 1):
            if isinstance(result, Exception):
                logger.error(f"   会话{i}: 异常 - {result}")
            else:
                success, filename, duration, chunks, bytes_count = result
                if success:
                    successful_count += 1
                    total_duration += duration
                    total_chunks += chunks
                    total_bytes += bytes_count
                    if filename:
                        audio_files.append(filename)
                    logger.info(f"   ✅ 会话{i}: {duration:.2f}秒, {chunks}块, {bytes_count}字节")
                else:
                    logger.error(f"   会话{i}: 失败")
        
        logger.info(f"🎯 总体统计:")
        logger.info(f"   成功率: {successful_count}/{len(concurrent_texts)} ({successful_count/len(concurrent_texts)*100:.1f}%)")
        logger.info(f"   平均耗时: {total_duration/max(successful_count,1):.2f}秒")
        logger.info(f"   总音频块: {total_chunks}")
        logger.info(f"   总字节数: {total_bytes}")
        
        # 播放所有生成的音频
        if audio_files:
            logger.info(f"🔊 依次播放 {len(audio_files)} 个音频文件:")
            for i, filename in enumerate(audio_files, 1):
                logger.info(f"   播放文件{i}: {filename}")
                play_audio(filename)
                if i < len(audio_files):  # 不是最后一个文件
                    input(f"   按回车继续播放下一个音频...")
            
            input("🎵 所有音频播放完成，按回车键继续...")
        
        return successful_count == len(concurrent_texts)
        
    except Exception as e:
        logger.error(f"并发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("🎵 豆包TTS测试套件")
    logger.info("=" * 60)
    
    # 检查输出目录
    output_dir = Path("log/audio")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    test_results = []
    
    # 测试实时流式播放TTS
    logger.info("🎉 首先体验真正的流式播放效果！")
    result0 = await test_tts_streaming_with_realtime_playback()
    test_results.append(("实时流式播放", result0))
    
    # 测试流式TTS（模拟LLM流式输出）
    result1 = await test_tts_streaming()
    test_results.append(("流式TTS", result1))
    
    # 测试文件输出
    result2 = await test_tts_file_output()
    test_results.append(("文件输出", result2))
    
    # 性能测试
    result3 = await test_tts_performance()
    test_results.append(("性能测试", result3))
    
    # 多会话并发测试
    result4 = await test_tts_streaming_multi_session()
    test_results.append(("多会话并发", result4))
    
    # 总结
    logger.info("📋 测试结果总结")
    logger.info("=" * 30)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    logger.info(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！豆包TTS功能正常")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和网络连接")
    
    logger.info("💡 注意事项:")
    logger.info("   1. 确保在 config/config.yaml 中正确配置豆包TTS凭证")
    logger.info("      在 api_key.doubao_tts 下设置 app_id 和 access_token")
    logger.info("   2. 确保网络连接正常，能够访问豆包TTS服务")
    logger.info("   3. 生成的音频文件保存在log/audio目录下")
    logger.info("   4. 实时流式播放需要安装 ffmpeg (brew install ffmpeg / apt install ffmpeg)")
    logger.info("      或安装 mpv/mplayer 等支持流式播放的播放器")
    logger.info("📝 配置示例:")
    logger.info("api_key:")
    logger.info("  doubao_tts:")
    logger.info("    app_id: your-app-id")
    logger.info("    access_token: \"your-access-token\"")
    logger.info("tts:")
    logger.info("  provider: \"doubao\"")
    logger.info("  speaker: \"zh_female_shuangkuaisisi_moon_bigtts\"")
    logger.info("  audio_format: \"mp3\"")
    logger.info("  sample_rate: 24000")


if __name__ == "__main__":
    asyncio.run(main())