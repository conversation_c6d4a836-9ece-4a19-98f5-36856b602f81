import asyncio
import json
import uuid
import time
from typing import Optional, AsyncGenerator, Dict, Any
import websockets
import fastrand  # pip install fastrand

from logger import get_logger


# Protocol constants from Doubao TTS WebSocket demo
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Types
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# Message Type Specific Flags
MsgTypeFlagWithEvent = 0b100

# Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001

# Compression
COMPRESSION_NO = 0b0000

# Events
EVENT_NONE = 0
EVENT_Start_Connection = 1
EVENT_FinishConnection = 2
EVENT_ConnectionStarted = 50
EVENT_ConnectionFailed = 51
EVENT_ConnectionFinished = 52
EVENT_StartSession = 100
EVENT_FinishSession = 102
EVENT_SessionStarted = 150
EVENT_SessionFinished = 152
EVENT_SessionFailed = 153
EVENT_TaskRequest = 200
EVENT_TTSSentenceStart = 350
EVENT_TTSSentenceEnd = 351
EVENT_TTSResponse = 352


class Header:
    def __init__(self,
                 protocol_version=PROTOCOL_VERSION,
                 header_size=DEFAULT_HEADER_SIZE,
                 message_type: int = 0,
                 message_type_specific_flags: int = 0,
                 serial_method: int = NO_SERIALIZATION,
                 compression_type: int = COMPRESSION_NO,
                 reserved_data=0):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_specific_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class Optional:
    def __init__(self, event: int = EVENT_NONE, sessionId: str = None, sequence: int = None):
        self.event = event
        self.sessionId = sessionId
        self.errorCode: int = 0
        self.connectionId: str = None
        self.response_meta_json: str = None
        self.sequence = sequence

    def as_bytes(self) -> bytes:
        option_bytes = bytearray()
        if self.event != EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.sessionId is not None:
            session_id_bytes = str.encode(self.sessionId)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class Response:
    def __init__(self, header: Header, optional: Optional):
        self.optional = optional
        self.header = header
        self.payload: bytes = None
        self.payload_json: str = None


class DoubaoTTS:
    """Doubao TTS provider for bidirectional streaming text-to-speech conversion"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize DoubaoTTS with configuration"""
        self.logger = get_logger('DoubaoTTS')
        
        # Load configuration
        self.app_id = config.get('app_id')
        self.access_token = config.get('access_token')
        self.speaker = config.get('speaker', 'zh_female_shuangkuaisisi_moon_bigtts')
        self.audio_format = config.get('audio_format', 'mp3')
        self.sample_rate = config.get('sample_rate', 24000)
        
        # Doubao TTS WebSocket URL from config
        self.ws_url = config.get('ws_url', 'wss://openspeech.bytedance.com/api/v3/tts/bidirection')
        
        # Validate required config
        if not self.app_id or not self.access_token:
            raise ValueError("DoubaoTTS requires 'app_id' and 'access_token' in configuration")
        
        # Session management
        self.ws = None
        self.session_id = None
        self.session_active = False
        
        self.logger.info(f"DoubaoTTS initialized with speaker: {self.speaker}, format: {self.audio_format}")
    
    async def __aenter__(self):
        """Context manager entry - starts the TTS session"""
        await self.start_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - finishes the TTS session"""
        await self.finish_session()

    def _gen_log_id(self) -> str:
        """Generate log ID similar to the demo"""
        ts = int(time.time() * 1000)
        r = fastrand.pcg32bounded(1 << 24) + (1 << 20)
        local_ip = "00000000000000000000000000000000"
        return f"02{ts}{local_ip}{r:08x}"

    def _get_ws_headers(self) -> Dict[str, str]:
        """Get WebSocket headers for authentication"""
        log_id = self._gen_log_id()
        return {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.access_token,
            "X-Api-Resource-Id": 'volc.service_type.10029',
            "X-Api-Connect-Id": str(uuid.uuid4()),
            "X-Tt-Logid": log_id,
        }

    async def _send_event(self, ws, header: bytes, optional: bytes = None, payload: bytes = None):
        """Send event to WebSocket"""
        full_client_request = bytearray(header)
        if optional is not None:
            full_client_request.extend(optional)
        if payload is not None:
            payload_size = len(payload).to_bytes(4, 'big', signed=True)
            full_client_request.extend(payload_size)
            full_client_request.extend(payload)
        await ws.send(full_client_request)

    def _read_res_content(self, res: bytes, offset: int):
        """Read response content from bytes"""
        content_size = int.from_bytes(res[offset: offset + 4])
        offset += 4
        content = str(res[offset: offset + content_size], encoding='utf8')
        offset += content_size
        return content, offset

    def _read_res_payload(self, res: bytes, offset: int):
        """Read response payload from bytes"""
        payload_size = int.from_bytes(res[offset: offset + 4])
        offset += 4
        payload = res[offset: offset + payload_size]
        offset += payload_size
        return payload, offset

    def _parse_response(self, res) -> Response:
        """Parse WebSocket response"""
        if isinstance(res, str):
            raise RuntimeError(res)
        
        response = Response(Header(), Optional())
        
        # Parse header
        header = response.header
        num = 0b00001111
        header.protocol_version = res[0] >> 4 & num
        header.header_size = res[0] & 0x0f
        header.message_type = (res[1] >> 4) & num
        header.message_type_specific_flags = res[1] & 0x0f
        header.serialization_method = res[2] >> num
        header.message_compression = res[2] & 0x0f
        header.reserved = res[3]
        
        offset = 4
        optional = response.optional
        
        if header.message_type in (FULL_SERVER_RESPONSE, AUDIO_ONLY_RESPONSE):
            # Read event
            if header.message_type_specific_flags == MsgTypeFlagWithEvent:
                optional.event = int.from_bytes(res[offset:8])
                offset += 4
                
                if optional.event == EVENT_NONE:
                    return response
                elif optional.event == EVENT_ConnectionStarted:
                    optional.connectionId, offset = self._read_res_content(res, offset)
                elif optional.event == EVENT_ConnectionFailed:
                    optional.response_meta_json, offset = self._read_res_content(res, offset)
                elif optional.event in (EVENT_SessionStarted, EVENT_SessionFailed, EVENT_SessionFinished):
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    optional.response_meta_json, offset = self._read_res_content(res, offset)
                elif optional.event == EVENT_TTSResponse:
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    response.payload, offset = self._read_res_payload(res, offset)
                elif optional.event in (EVENT_TTSSentenceEnd, EVENT_TTSSentenceStart):
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    response.payload_json, offset = self._read_res_content(res, offset)
        
        elif header.message_type == ERROR_INFORMATION:
            optional.errorCode = int.from_bytes(res[offset:offset + 4], "big", signed=True)
            offset += 4
            response.payload, offset = self._read_res_payload(res, offset)
        
        return response

    def _get_payload_bytes(self, uid='1234', event=EVENT_NONE, text='', speaker='', 
                          audio_format='mp3', audio_sample_rate=24000):
        """Generate payload bytes for request"""
        return str.encode(json.dumps({
            "user": {"uid": uid},
            "event": event,
            "namespace": "BidirectionalTTS",
            "req_params": {
                "text": text,
                "speaker": speaker,
                "audio_params": {
                    "format": audio_format,
                    "sample_rate": audio_sample_rate,
                    "enable_timestamp": True,
                }
            }
        }))

    async def _start_connection(self, ws):
        """Start connection"""
        header = Header(message_type=FULL_CLIENT_REQUEST, 
                       message_type_specific_flags=MsgTypeFlagWithEvent).as_bytes()
        optional = Optional(event=EVENT_Start_Connection).as_bytes()
        payload = str.encode("{}")
        return await self._send_event(ws, header, optional, payload)

    async def _start_session(self, ws, speaker, session_id):
        """Start session"""
        header = Header(message_type=FULL_CLIENT_REQUEST,
                       message_type_specific_flags=MsgTypeFlagWithEvent,
                       serial_method=JSON).as_bytes()
        optional = Optional(event=EVENT_StartSession, sessionId=session_id).as_bytes()
        payload = self._get_payload_bytes(event=EVENT_StartSession, speaker=speaker)
        return await self._send_event(ws, header, optional, payload)

    async def _send_text(self, ws, speaker, text, session_id):
        """Send text for TTS"""
        header = Header(message_type=FULL_CLIENT_REQUEST,
                       message_type_specific_flags=MsgTypeFlagWithEvent,
                       serial_method=JSON).as_bytes()
        optional = Optional(event=EVENT_TaskRequest, sessionId=session_id).as_bytes()
        payload = self._get_payload_bytes(event=EVENT_TaskRequest, text=text, speaker=speaker,
                                        audio_format=self.audio_format, audio_sample_rate=self.sample_rate)
        return await self._send_event(ws, header, optional, payload)

    async def _finish_session(self, ws, session_id):
        """Finish session"""
        header = Header(message_type=FULL_CLIENT_REQUEST,
                       message_type_specific_flags=MsgTypeFlagWithEvent,
                       serial_method=JSON).as_bytes()
        optional = Optional(event=EVENT_FinishSession, sessionId=session_id).as_bytes()
        payload = str.encode('{}')
        return await self._send_event(ws, header, optional, payload)

    async def _finish_connection(self, ws):
        """Finish connection"""
        header = Header(message_type=FULL_CLIENT_REQUEST,
                       message_type_specific_flags=MsgTypeFlagWithEvent,
                       serial_method=JSON).as_bytes()
        optional = Optional(event=EVENT_FinishConnection).as_bytes()
        payload = str.encode('{}')
        return await self._send_event(ws, header, optional, payload)

    async def start_session(self) -> bool:
        """
        Start a TTS session that can handle multiple text segments
            
        Returns:
            bool: True if session started successfully
        """
        if self.session_active:
            self.logger.warning("TTS session already active")
            return True
            
        try:
            ws_headers = self._get_ws_headers()
            self.ws = await websockets.connect(
                self.ws_url, 
                additional_headers=ws_headers, 
                max_size=1000000000
            )
            
            # Start connection
            await self._start_connection(self.ws)
            res = self._parse_response(await self.ws.recv())
            if res.optional.event != EVENT_ConnectionStarted:
                raise RuntimeError(f"Start connection failed: {res.optional.__dict__}")
            
            # Start session
            self.session_id = str(uuid.uuid4()).replace('-', '')
            await self._start_session(self.ws, self.speaker, self.session_id)
            res = self._parse_response(await self.ws.recv())
            if res.optional.event != EVENT_SessionStarted:
                raise RuntimeError(f"Start session failed: {res.optional.__dict__}")
            
            self.session_active = True
            self.logger.info(f"TTS session started: {self.session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting TTS session: {e}")
            if self.ws:
                await self.ws.close()
                self.ws = None
            return False
    
    async def send_text_segment(self, text: str) -> bool:
        """
        Send a text segment to the active TTS session
        
        Args:
            text: Text segment to convert to speech
            
        Returns:
            bool: True if text sent successfully
        """
        if not self.session_active or not self.ws:
            self.logger.error("No active TTS session. Call start_session() first.")
            return False
            
        if not text or not text.strip():
            self.logger.warning("Empty text provided for TTS")
            return False
        
        text = text.strip()
        try:
            await self._send_text(self.ws, self.speaker, text, self.session_id)
            self.logger.info(f"Text sent to TTS: {text[:50]}{'...' if len(text) > 50 else ''}")
            return True
        except Exception as e:
            self.logger.error(f"Error sending text to TTS: {e}")
            return False
    
    async def get_audio_stream(self) -> AsyncGenerator[bytes, None]:
        """
        Get audio stream from the active TTS session
        
        Yields:
            bytes: Audio data chunks as they arrive
        """
        if not self.session_active or not self.ws:
            self.logger.error("No active TTS session")
            return
            
        try:
            async for audio_chunk in self._receive_audio_stream_generator(self.ws):
                yield audio_chunk
        except Exception as e:
            self.logger.error(f"Error receiving audio stream: {e}")
    
    async def finish_session(self) -> bool:
        """
        Finish the current TTS session
            
        Returns:
            bool: True if session finished successfully
        """
        if not self.session_active or not self.ws:
            self.logger.warning("No active TTS session to finish")
            return True
            
        try:
            # Finish session
            await self._finish_session(self.ws, self.session_id)
            
            # Finish connection
            await self._finish_connection(self.ws)
            try:
                res = self._parse_response(await self.ws.recv())
                self.logger.debug(f"Finish connection response: {res.optional.event}")
            except:
                pass  # Connection might already be closed
            
            await self.ws.close()
            
            self.logger.info(f"TTS session finished: {self.session_id}")
            
            # Reset session state
            self.ws = None
            self.session_id = None
            self.session_active = False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error finishing TTS session: {e}")
            # Force cleanup
            if self.ws:
                await self.ws.close()
            self.ws = None
            self.session_id = None
            self.session_active = False
            return False
    
    async def text_to_speech_stream(self, text: str) -> AsyncGenerator[bytes, None]:
        """
        Convert single text segment to speech (convenience method for single-use)
        
        Args:
            text: Text segment to convert to speech
            
        Yields:
            bytes: Audio data chunks as they arrive
        """
        try:
            # Start session
            if not await self.start_session():
                self.logger.error("Failed to start TTS session")
                return
            
            # Send text
            if not await self.send_text_segment(text):
                self.logger.error("Failed to send text to TTS")
                return
            
            # Immediately finish session to signal end of input
            await self._finish_session(self.ws, self.session_id)
            
            # Get audio stream
            async for chunk in self.get_audio_stream():
                yield chunk
            
            # Clean up session
            await self.finish_session()
                
        except Exception as e:
            self.logger.error(f"Error in TTS streaming: {e}")
            # Ensure cleanup
            if self.session_active:
                await self.finish_session()
    
    async def _receive_audio_stream_generator(self, ws):
        """Receive audio stream and yield chunks as they arrive"""
        audio_count = 0
        
        while True:
            try:
                raw_response = await ws.recv()
                res = self._parse_response(raw_response)
                
                if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                    if res.payload:
                        audio_count += 1
                        self.logger.debug(f"Received audio chunk {audio_count}: {len(res.payload)} bytes")
                        yield res.payload
                        
                elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                    self.logger.debug(f"TTS sentence event: {res.optional.event}")
                    continue
                    
                elif res.optional.event == EVENT_SessionFinished:
                    self.logger.info("TTS session finished")
                    break
                    
                else:
                    self.logger.debug(f"Other event: {res.optional.event}")
                    if res.optional.event in [EVENT_ConnectionFinished, EVENT_ConnectionFailed]:
                        break
                        
            except websockets.exceptions.ConnectionClosed:
                self.logger.info("TTS WebSocket connection closed by server")
                break
            except Exception as e:
                self.logger.warning(f"Error receiving audio stream: {e}")
                break

