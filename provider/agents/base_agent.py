"""
Base agent class for LangGraph-based agents with event subscription capabilities.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Set, Optional
from ..memory import Event


class BaseAgent(ABC):
    """Abstract base class for all LangGraph agents with event subscription support"""
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        self.config = config
        self.logger = None
        self.graph = None
        self._subscribed_events: Set[str] = set()
    
    def set_logger(self, logger):
        """Set logger for the agent"""
        self.logger = logger
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the agent and build LangGraph workflow"""
        pass
    
    @abstractmethod
    def get_subscribed_events(self) -> List[str]:
        """Return list of event types this agent wants to subscribe to"""
        pass
    
    async def handle_event(self, event: Event) -> None:
        """Handle incoming event - routes to specific handler based on event type"""
        if event.type not in self._subscribed_events:
            return
        
        try:
            # Route to specific handler method
            handler_method = f"_handle_{event.type}_event"
            if hasattr(self, handler_method):
                handler = getattr(self, handler_method)
                await handler(event)
            else:
                if self.logger:
                    self.logger.warning(f"No handler found for event type: {event.type}")
        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling {event.type} event in {self.__class__.__name__}: {e}")
    
    def subscribe_to_events(self, event_bus, session_id: Optional[str] = None):
        """Subscribe this agent to its interested events on the event bus"""
        subscribed_events = self.get_subscribed_events()
        self._subscribed_events = set(subscribed_events)
        
        for event_type in subscribed_events:
            # Create a wrapper that filters by session_id if provided
            if session_id:
                async def event_handler(event: Event, agent=self, target_session=session_id):
                    if event.session_id == target_session:
                        await agent.handle_event(event)
            else:
                async def event_handler(event: Event, agent=self):
                    await agent.handle_event(event)
            
            event_bus.subscribe(event_type, event_handler)
            
            if self.logger:
                self.logger.info(f"{self.__class__.__name__} subscribed to {event_type} events")
    
    # Abstract event handlers - subclasses implement only what they need
    async def _handle_audio_event(self, event: Event) -> None:
        """Handle audio events - override in subclass if needed"""
        pass
    
    async def _handle_text_event(self, event: Event) -> None:
        """Handle text events - override in subclass if needed"""
        pass
    
    async def _handle_search_result_event(self, event: Event) -> None:
        """Handle search result events - override in subclass if needed"""
        pass
    
    async def _handle_interruption_event(self, event: Event) -> None:
        """Handle interruption events - override in subclass if needed"""
        pass
    
    async def _handle_agent_status_event(self, event: Event) -> None:
        """Handle agent status events - override in subclass if needed"""
        pass