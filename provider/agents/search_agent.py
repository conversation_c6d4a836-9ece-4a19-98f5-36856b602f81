from typing import Dict, Any, List, Optional, Annotated, TypedDict
from datetime import datetime
import asyncio
import yaml
import os
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.types import Command
from ..memory import Event
from .base_agent import BaseAgent
from ..llm import llm_factory


class SearchState(TypedDict):
    """State schema for SearchAgent"""
    messages: Annotated[List, add_messages]
    audio_file: Optional[str]
    session_id: str
    user_input: Optional[str]
    is_greeting: bool
    needs_search: bool
    search_query: Optional[str]
    search_results: Optional[str]
    evaluation_result: Optional[str]
    is_answer_complete: bool
    retry_count: int
    max_retries: int
    final_result: Optional[str]


class SearchAgent(BaseAgent):
    """LangGraph-based agent for deep analysis and search using Gemini 2.5 Pro Thinking"""
    
    def __init__(self, config: Dict[str, Any], event_bus=None):
        super().__init__(config)
        self.event_bus = event_bus  # EventBus to send results to other agents
        self.gemini_provider = None
        
        # Load SearchAgent configuration
        self.search_config = self._load_search_config()
        
        # Get max retry attempts from config
        search_config = config.get('llm', {}).get('agents', {}).get('search', {})
        self.max_retries = search_config.get('max_retry_attempts', 3)
        

    def _load_search_config(self) -> Dict[str, Any]:
        """Load SearchAgent configuration from YAML file"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'search_agent_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            if hasattr(self, 'logger') and self.logger:
                self.logger.error(f"Failed to load search agent config: {e}")
            # Return default config
            return {
                'greeting_phrases': ["hi~", "hi", "hello", "你好", "在吗"],
                'system_prompts': {
                    'main_search_prompt': '你是一个智能语音处理和搜索助手。',
                    'evaluation_prompt': '评估搜索结果是否充分回答了用户的问题。只回答：complete 或 incomplete'
                },
                'json_schema': {
                    'type': 'object',
                    'properties': {},
                    'required': []
                }
            }
        
    def get_subscribed_events(self) -> List[str]:
        """Return events this agent subscribes to"""
        return ["audio", "text"]
        
    async def initialize(self) -> bool:
        """Initialize the SearchAgent with LangGraph and Gemini 2.5 Pro"""
        try:
            # Initialize LLM provider for SearchAgent
            self.gemini_provider = llm_factory.create_llm_provider(self.config, 'search')
            
            # Build LangGraph
            self.graph = self._build_graph()
            
            if self.logger:
                self.logger.info("SearchAgent initialized with LangGraph and Gemini 2.5 Pro Thinking")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize SearchAgent: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build LangGraph workflow for search and analysis"""
        workflow = StateGraph(SearchState)
        
        # Add nodes
        workflow.add_node("analyze_content", self._analyze_content_node)
        
        # Define flow
        workflow.add_edge(START, "analyze_content")
        
        # Direct to end - all processing is now async
        workflow.add_edge("analyze_content", END)
        
        return workflow.compile()
    
    async def _analyze_content_node(self, state: SearchState) -> SearchState:
        """Analyze input content and start background search if needed"""
        if self.logger:
            self.logger.info("SearchAgent analyzing content")
        
        try:
            # Extract user input
            user_input = ""
            if state.get("audio_file"):
                # For audio, extract transcript first (placeholder for now)
                user_input = f"[Audio file: {state['audio_file']}]"
                if self.logger:
                    self.logger.info(f"Processing audio input: {user_input}")
            elif state.get("messages"):
                # For text messages, get the content
                human_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
                if human_messages:
                    user_input = human_messages[-1].content
                    if self.logger:
                        self.logger.info(f"Processing text input: {user_input}")
            
            # Check if it's a greeting
            greeting_phrases = self.search_config.get('greeting_phrases', [])
            is_greeting = user_input.strip().lower() in greeting_phrases
            if self.logger:
                self.logger.info(f"Is greeting check: {is_greeting}")
            
            # If it's a greeting, exit early
            if is_greeting:
                if self.logger:
                    self.logger.info("Detected greeting, exiting SearchAgent workflow")
                return {
                    **state,
                    "user_input": user_input,
                    "is_greeting": True,
                    "needs_search": False,
                    "retry_count": 0,
                    "max_retries": self.max_retries
                }
            
            # Start background search task - don't wait for it!
            audio_file_path = state.get("audio_file")
            session_id = state.get("session_id", "unknown")
            
            if self.logger:
                self.logger.info(f"Starting background search for session {session_id}")
            
            # Create background task for search
            asyncio.create_task(self._background_search_task(
                user_input=user_input,
                audio_file=audio_file_path,
                session_id=session_id,
                retry_count=0
            ))
            
            # Return immediately - search will happen in background
            return {
                **state,
                "user_input": user_input,
                "is_greeting": False,
                "needs_search": True,  # Assume we need search for non-greetings
                "retry_count": 0,
                "max_retries": self.max_retries
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in content analysis: {e}")
            return {
                **state,
                "user_input": "",
                "is_greeting": False,
                "needs_search": False,
                "retry_count": 0,
                "max_retries": self.max_retries
            }
    
    async def _background_search_task(self, user_input: str, audio_file: str, session_id: str, retry_count: int):
        """Background task to perform search and send results via event bus"""
        try:
            if self.logger:
                self.logger.info(f"Background search started for session {session_id}")
            
            # Perform the search operation
            search_result = await self._check_and_search(user_input, audio_file)
            
            if search_result is None:
                if self.logger:
                    self.logger.info(f"No search needed for session {session_id}")
                return
            
            # Extract results
            transcribed_text = search_result.get('transcribed_text', user_input)
            needs_search = search_result.get('needs_search', False)
            search_results = search_result.get('search_results', '')
            search_reasoning = search_result.get('search_reasoning', '')
            
            if self.logger:
                self.logger.info(f"Background search completed for session {session_id}")
                self.logger.info(f"Transcribed text: '{transcribed_text}' (was: '{user_input}')")
                self.logger.info(f"Needs search: {needs_search}")
            
            if not needs_search:
                if self.logger:
                    self.logger.info(f"Search not needed: {search_reasoning}")
                return
            
            if not search_results:
                if self.logger:
                    self.logger.warning(f"Search needed but no results returned for session {session_id}")
                return
            
            # Evaluate search results
            evaluation_result = await self._evaluate_search_results(
                transcribed_text, search_results, retry_count
            )
            
            if evaluation_result == "complete":
                # Send successful search results via event bus
                await self._send_search_results_via_event_bus(
                    session_id=session_id,
                    user_question=transcribed_text,
                    search_results=search_results,
                    retry_count=retry_count
                )
            else:
                # Handle retry logic if needed
                if retry_count < self.max_retries:
                    if self.logger:
                        self.logger.info(f"Search result incomplete, retrying ({retry_count + 1}/{self.max_retries})")
                    # Could implement retry logic here
                else:
                    if self.logger:
                        self.logger.warning(f"Max retries reached for session {session_id}")
                        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in background search for session {session_id}: {e}")
    
    async def _evaluate_search_results(self, user_question: str, search_results: str, retry_count: int) -> str:
        """Evaluate if search results adequately answer the user's question"""
        try:
            if self.logger:
                self.logger.info(f"Evaluating answer quality (retry: {retry_count})")
                self.logger.info(f"User question: {user_question}")
                self.logger.info(f"Search result length: {len(search_results)}")
            
            system_prompt = self.search_config.get('system_prompts', {}).get('evaluation_prompt', '')
            evaluation_schema = self.search_config.get('evaluation_schema', {})
            
            user_context = f"用户问题：{user_question}\n\n搜索结果：{search_results}\n\n请评估这个搜索结果是否充分回答了用户的问题。"
            
            # Direct async call to search provider with JSON schema
            result = await self.gemini_provider.search_and_generate(
                query=user_context,
                system_prompt=system_prompt,
                enable_search=False,
                enable_url_context=False,
                response_schema=evaluation_schema
            )
            
            response_text = result.get("text", "").strip()
            
            if self.logger:
                self.logger.debug(f"Evaluation LLM response: {response_text}")
            
            # Parse JSON response
            try:
                import json
                # Try to extract JSON from response (in case LLM adds extra text)
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    parsed_result = json.loads(json_text)
                    
                    is_complete = parsed_result.get('is_complete', False)
                    reason = parsed_result.get('reason', '')
                    
                    if self.logger:
                        self.logger.info(f"Evaluation result: is_complete={is_complete}, reason={reason}")
                    
                    return "complete" if is_complete else "incomplete"
                else:
                    if self.logger:
                        self.logger.error("Failed to find valid JSON in evaluation response")
                    # Fallback to simple text check
                    if "complete" in response_text.lower():
                        return "complete"
                    else:
                        return "incomplete"
                    
            except json.JSONDecodeError as e:
                if self.logger:
                    self.logger.error(f"Failed to parse JSON in evaluation response: {e}")
                    self.logger.debug(f"Raw evaluation response: {response_text}")
                # Fallback to simple text check
                if "complete" in response_text.lower():
                    return "complete"
                else:
                    return "incomplete"
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in evaluation: {e}")
            return "complete"  # Default to complete on error
    
    async def _send_search_results_via_event_bus(self, session_id: str, user_question: str, search_results: str, retry_count: int):
        """Send search results to FastReplyAgent via event bus"""
        try:
            if self.logger:
                self.logger.info("SearchAgent sending successful search results to FastReplyAgent")
                self.logger.info(f"Preparing to send search results (retry: {retry_count})")
                self.logger.info(f"Results length: {len(search_results)}")
                self.logger.info(f"Is complete: True")
            
            # Create search result event
            from ..memory import Event
            from datetime import datetime
            
            search_event = Event(
                id=f"search_result_{session_id}_{datetime.now().timestamp()}",
                type="search_result",
                data={
                    "user_question": user_question,
                    "search_results": search_results,
                    "retry_count": retry_count,
                    "is_complete": True
                },
                timestamp=datetime.now(),
                session_id=session_id
            )
            
            # Publish via event bus
            if self.event_bus:
                await self.event_bus.publish(search_event)
                if self.logger:
                    self.logger.info("Successful search results sent to FastReplyAgent via event bus")
            else:
                if self.logger:
                    self.logger.error("Event bus not available")
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending search results via event bus: {e}")
    
    async def _check_and_search(self, user_input: str, audio_file: str = None) -> Optional[Dict[str, Any]]:
        """Check if search is needed, and if so, perform search in one step using JSON Schema
        
        Returns:
            Dict with keys: 'transcribed_text', 'search_results' (if search needed), or None if no search needed
        """
        try:
            # Check if gemini_provider supports search (GeminiSearchProvider)
            if hasattr(self.gemini_provider, 'search_and_generate'):
                if self.logger:
                    self.logger.info("Using GeminiSearchProvider with JSON Schema for intelligent processing")
                
                # Get JSON Schema and system prompt from config
                json_schema = self.search_config.get('json_schema', {})
                main_prompt = self.search_config.get('system_prompts', {}).get('main_search_prompt', '')
                
                # Build system prompt with JSON schema
                import json
                schema_str = json.dumps(json_schema, ensure_ascii=False, indent=2)
                system_prompt = f"{main_prompt}\n\nJSON Schema:\n{schema_str}"
                
                # Prepare query with audio file info if provided
                if audio_file:
                    query_with_context = f"语音文件路径: {audio_file}\n用户输入内容: {user_input}"
                else:
                    query_with_context = f"用户输入内容: {user_input}"
                
                # DEBUG: Log LLM call
                if self.logger:
                    formatted_query = query_with_context[:100] + '...' + query_with_context[-100:] if len(query_with_context) > 200 else query_with_context
                    self.logger.debug(f"Calling search_and_generate with JSON Schema query: {formatted_query}")
                
                # Direct async call to search provider (no thread pool needed)
                result = await self.gemini_provider.search_and_generate(
                    query=query_with_context,
                    system_prompt=system_prompt,
                    enable_search=True,
                    enable_url_context=True,
                    enable_thinking=True,
                    response_schema=json_schema
                )
                
                response_text = result.get("text", "").strip()
                url_metadata = result.get("url_context_metadata", "")
                
                # DEBUG: Log LLM output
                if self.logger:
                    self.logger.debug(f"LLM JSON response: {response_text[:300]}{'...' if len(response_text) > 300 else ''}")
                    if url_metadata:
                        self.logger.debug(f"URL metadata: {url_metadata}")
                        self.logger.info(f"URL metadata: {url_metadata}")
                
                # Parse JSON response
                try:
                    import json
                    # Try to extract JSON from response (in case LLM adds extra text)
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_text = response_text[json_start:json_end]
                        parsed_result = json.loads(json_text)
                        
                        if self.logger:
                            self.logger.info(f"Parsed JSON result: {parsed_result}")
                            self.logger.info(f"Transcribed text: {parsed_result.get('transcribed_text', 'N/A')}")
                            self.logger.info(f"Needs search: {parsed_result.get('needs_search', 'N/A')}")
                            self.logger.info(f"Search reasoning: {parsed_result.get('search_reasoning', 'N/A')}")
                        
                        # Update user_input with corrected transcription if available
                        corrected_text = parsed_result.get('transcribed_text', user_input)
                        if corrected_text != user_input:
                            if self.logger:
                                self.logger.info(f"Text correction: '{user_input}' -> '{corrected_text}'")
                        
                        # Return structured result with transcribed text and search info
                        needs_search = parsed_result.get('needs_search', False)
                        result = {
                            'transcribed_text': corrected_text,
                            'needs_search': needs_search,
                            'search_reasoning': parsed_result.get('search_reasoning', ''),
                            'search_results': parsed_result.get('search_results', '') if needs_search else ''
                        }
                        
                        if self.logger:
                            if needs_search:
                                self.logger.info(f"Search completed with structured results, length: {len(result['search_results'])}")
                            else:
                                self.logger.info(f"No search needed: {result['search_reasoning']}")
                        
                        return result
                    else:
                        if self.logger:
                            self.logger.error("Failed to find valid JSON in LLM response")
                        return None
                        
                except json.JSONDecodeError as e:
                    if self.logger:
                        self.logger.error(f"Failed to parse JSON response: {e}")
                        self.logger.debug(f"Raw response: {response_text}")
                    # Fallback to treating the response as plain text
                    if "需要搜索" not in response_text and "NO_SEARCH_NEEDED" not in response_text:
                        return response_text
                    else:
                        return None
            else:
                # No search provider available, return "no search needed" result
                if self.logger:
                    self.logger.warning("No search provider available, returning no search needed")
                
                return {
                    'transcribed_text': user_input,
                    'needs_search': False,
                    'search_reasoning': '没有可用的搜索功能',
                    'search_results': ''
                }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in check and search: {e}")
            return None
    
    
    async def cleanup(self):
        """Clean up resources"""
        pass
