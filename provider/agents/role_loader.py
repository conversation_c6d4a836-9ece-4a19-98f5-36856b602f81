import yaml
import os
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from pathlib import Path


class RoleLoader:
    """Load role configurations from YAML file"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            # Default to config/roles.yaml relative to project root
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "roles.yaml"
        
        self.config_path = Path(config_path)
        self._roles_cache = None
        self._default_role = None
    
    def _load_config(self) -> Dict:
        """Load and cache the roles configuration"""
        if self._roles_cache is None:
            try:
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    config = yaml.safe_load(file)
                    self._roles_cache = config.get('roles', {})
                    self._default_role = config.get('default_role', '')
            except FileNotFoundError:
                raise FileNotFoundError(f"Role configuration file not found: {self.config_path}")
            except yaml.YAMLError as e:
                raise ValueError(f"Invalid YAML in role configuration: {e}")
        
        return self._roles_cache
    
    def get_role_config(self, role_name: Optional[str] = None) -> Tuple[str, str]:
        """
        Get role configuration by name
        
        Args:
            role_name: Name of the role, if None uses default_role
            
        Returns:
            Tuple of (prompt, voice) for the role
            
        Raises:
            ValueError: If role not found
        """
        roles = self._load_config()
        
        # Use default role if role_name is None
        if role_name is None:
            role_name = self._default_role
            if not role_name:
                raise ValueError("No default role specified in configuration")
        
        if role_name not in roles:
            available_roles = list(roles.keys())
            raise ValueError(f"Role '{role_name}' not found. Available roles: {available_roles}")
        
        role_config = roles[role_name]
        prompt = role_config.get('prompt', '')
        voice = role_config.get('voice', '')
        
        if not prompt:
            raise ValueError(f"No prompt found for role '{role_name}'")
        if not voice:
            raise ValueError(f"No voice found for role '{role_name}'")
        
        return prompt, voice
    
    def get_available_roles(self) -> list:
        """Get list of available role names"""
        roles = self._load_config()
        return list(roles.keys())
    
    def get_default_role(self) -> str:
        """Get the default role name"""
        self._load_config()  # Ensure config is loaded
        return self._default_role or ''


# Convenience functions for direct use
def load_role(role_name: Optional[str] = None, config_path: Optional[str] = None,
              default_prompt: str = "你是一个AI助手，请简短、自然地回复用户。",
              default_voice: str = "zh_female_shuangkuaisisi_emo_v2_mars_bigtts") -> Tuple[str, str]:
    """
    Load role configuration with safe fallbacks
    
    Args:
        role_name: Name of the role, if None uses default_role
        config_path: Path to the configuration file, if None uses default path
        default_prompt: Default prompt to use if role loading fails
        default_voice: Default voice to use if role loading fails
        
    Returns:
        Tuple of (prompt, voice) for the role or defaults
    """
    try:
        loader = RoleLoader(config_path)
        return loader.get_role_config(role_name)
    except (FileNotFoundError, ValueError, yaml.YAMLError) as e:
        # Return defaults if any error occurs
        return default_prompt, default_voice


def get_system_prompt_for_role(role_name: Optional[str] = None, config_path: Optional[str] = None, 
                               default_prompt: str = "你是一个AI助手，请简短、自然地回复用户。") -> str:
    """
    Get system prompt for a role with fallback to default
    
    Args:
        role_name: Name of the role, if None uses default_role
        config_path: Path to the configuration file, if None uses default path
        default_prompt: Default prompt to use if role loading fails
        
    Returns:
        System prompt string for the role or default prompt
    """
    prompt, _ = load_role(role_name, config_path, default_prompt)
    return prompt


def get_voice_for_role(role_name: Optional[str] = None, config_path: Optional[str] = None,
                       default_voice: str = "zh_female_shuangkuaisisi_emo_v2_mars_bigtts") -> str:
    """
    Get voice configuration for a role with fallback to default
    
    Args:
        role_name: Name of the role, if None uses default_role
        config_path: Path to the configuration file, if None uses default path
        default_voice: Default voice to use if role loading fails
        
    Returns:
        Voice parameter string for TTS or default voice
    """
    _, voice = load_role(role_name, config_path, default_voice=default_voice)
    return voice