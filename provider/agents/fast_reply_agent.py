from typing import Dict, Any, List, Optional, Annotated, TypedDict
from datetime import datetime
import asyncio
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import Runnable
from langgraph.types import Command
from ..llm import llm_factory
from ..memory import Event
from .base_agent import BaseAgent
from .role_loader import load_role


class FastReplyState(TypedDict):
    """State schema for FastReplyAgent"""
    messages: Annotated[List, add_messages]
    audio_file: Optional[str]
    session_id: str
    is_speaking: bool
    should_interrupt: bool
    search_result: Optional[str]
    response_ready: bool


class FastReplyAgent(BaseAgent):
    """LangGraph-based agent for quick multimodal responses"""
    
    def __init__(self, config: Dict[str, Any], connection_handler, event_bus=None, role_name: Optional[str] = None):
        super().__init__(config)
        self.connection_handler = connection_handler
        self.event_bus = event_bus
        self.llm_provider = None
        self.is_speaking = False
        self.role_name = role_name
        self.system_prompt, self.voice_config = load_role(self.role_name)
        self.connection_closed = False
    
    def get_voice_config(self) -> str:
        """Get voice configuration for TTS"""
        return self.voice_config
    
    def get_subscribed_events(self) -> List[str]:
        """Return events this agent subscribes to"""
        return ["audio", "text", "search_result", "interruption"]
        
    async def initialize(self) -> bool:
        """Initialize the FastReplyAgent with LangGraph"""
        try:
            # Initialize LLM provider for FastReplyAgent
            self.llm_provider = llm_factory.create_llm_provider(self.config, 'fast_reply')
            
            # Build LangGraph
            self.graph = self._build_graph()
            
            if self.logger:
                self.logger.info("FastReplyAgent initialized with LangGraph")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize FastReplyAgent: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build LangGraph workflow for fast reply"""
        if self.logger:
            self.logger.debug("Building FastReplyAgent workflow graph")
        
        workflow = StateGraph(FastReplyState)
        
        # Add nodes
        workflow.add_node("generate_quick_response", self._generate_quick_response_node)
        workflow.add_node("check_interruption", self._check_interruption_node)
        workflow.add_node("send_response", self._send_response_node)
        
        # Define simplified flow: START -> generate_quick_response -> check_interruption -> send_response/END
        workflow.add_edge(START, "generate_quick_response")
        workflow.add_edge("generate_quick_response", "check_interruption")
        workflow.add_conditional_edges(
            "check_interruption",
            self._route_after_interruption_check,
            {
                "send": "send_response",
                "stop": END,
                "regenerate": "generate_quick_response"
            }
        )
        workflow.add_edge("send_response", END)
        
        if self.logger:
            self.logger.debug("FastReplyAgent workflow graph built successfully")
        
        return workflow.compile()
    
    async def _generate_quick_response_node(self, state: FastReplyState) -> FastReplyState:
        """Generate quick response using multimodal LLM with streaming"""
        if self.logger:
            self.logger.info("FastReplyAgent generating quick response")
            self.logger.debug(f"State: audio_file={state.get('audio_file')}, messages={len(state.get('messages', []))}")
        
        try:
            if state.get("audio_file"):
                if self.logger:
                    self.logger.debug(f"Processing audio file: {state['audio_file']}")
                # Audio input processing with streaming
                response_generator = self._quick_audio_analysis_stream(state["audio_file"])
            else:
                # Text input processing with streaming
                last_message = state["messages"][-1] if state.get("messages") else None
                if last_message and isinstance(last_message, HumanMessage):
                    if self.logger:
                        self.logger.debug(f"Processing text input: {last_message.content[:50]}...")
                    # Check if we need to regenerate with search results
                    search_result = state.get("search_result") if state.get("regenerate_with_search") else None
                    response_generator = self._generate_text_response_stream(last_message.content, search_result)
                else:
                    if self.logger:
                        self.logger.debug("No valid input, using default response")
                    response_generator = self._generate_default_response_stream()
            
            # Start real-time streaming response with TTS to client
            if self.logger:
                self.logger.debug("Starting real-time streaming with TTS to client")
            await self._publish_llm_stream_event(response_generator, state)
            
            if self.logger:
                self.logger.info("Quick response generation completed, setting is_speaking=True")
            
            return {
                **state,
                "is_speaking": True,
                "should_interrupt": False
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error generating quick response: {e}")
            
            error_message = AIMessage(content="抱歉，我现在无法回应")
            return {
                **state,
                "messages": [error_message],
                "is_speaking": True
            }
    
    async def _check_interruption_node(self, state: FastReplyState) -> FastReplyState:
        """Check if response should be interrupted or enhanced with search results"""
        if self.logger:
            self.logger.debug("Checking for interruptions and search results")
        
        # Check for VadAgent interruption
        vad_interrupt = state.get("should_interrupt", False)
        if vad_interrupt:
            if self.logger:
                self.logger.info("VadAgent interruption detected - stopping response")
            return {
                **state,
                "should_interrupt": True,
                "interruption_type": "vad"
            }
        
        # Check for SearchAgent results
        search_result = state.get("search_result")
        if search_result and not state.get("search_processed", False):
            if self.logger:
                self.logger.info(f"SearchAgent result received - regenerating response with search data")
                self.logger.debug(f"Search result: {search_result[:100]}...")
            return {
                **state,
                "should_interrupt": False,
                "search_processed": True,
                "regenerate_with_search": True
            }
        
        if self.logger:
            self.logger.debug("No interruptions detected - proceeding normally")
        
        return {
            **state,
            "should_interrupt": False
        }
    
    def _route_after_interruption_check(self, state: FastReplyState) -> str:
        """Route based on interruption status"""
        if state.get("should_interrupt", False):
            if self.logger:
                self.logger.debug(f"Routing to stop due to interruption: {state.get('interruption_type', 'unknown')}")
            return "stop"
        elif state.get("regenerate_with_search", False):
            if self.logger:
                self.logger.debug("Routing to regenerate with search results")
            return "regenerate"
        else:
            if self.logger:
                self.logger.debug("Routing to send response")
            return "send"
    
    async def _send_response_node(self, state: FastReplyState) -> FastReplyState:
        """Finalize response and clean up state"""
        if self.logger:
            self.logger.info("FastReplyAgent finalizing response")
            self.logger.debug(f"Final state: is_speaking={state.get('is_speaking')}, messages={len(state.get('messages', []))}")
        
        # Since streaming is now handled in _generate_quick_response_node,
        # this node just finalizes the state
        return {
            **state,
            "is_speaking": False,
            "response_ready": True
        }
    
    async def _quick_audio_analysis_stream(self, audio_file_path: str):
        """Quick audio analysis using multimodal LLM with streaming"""
        try:
            user_context = "回复这段语音中的话。回复时考虑语音所能听出的环境、说话人情绪。如果有好几个人说话，你判断不了谁是主说话人，就说听不清；如果能分清，就回复主说话人的话。"
            
            if self.logger:
                self.logger.debug(f"Starting LLM audio analysis with file: {audio_file_path}")
            
            # Direct async streaming - no thread pool needed
            chunk_count = 0
            async for chunk in self.llm_provider.generate_stream(
                system_prompt=self.system_prompt,
                user_context=user_context,
                file_path=audio_file_path
            ):
                chunk_count += 1
                if self.logger:
                    self.logger.debug(f"LLM audio output chunk {chunk_count}: {chunk[:100]}{'...' if len(chunk) > 100 else ''}")
                yield chunk
            
            if self.logger:
                self.logger.debug(f"Audio analysis completed - Total chunks: {chunk_count}")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Quick audio analysis streaming error: {e}")
            yield "抱歉，我没有听清楚"
    
    async def _generate_text_response_stream(self, text: str, search_result: str = None):
        """Generate streaming response for text input, optionally with search results"""
        try:
            if search_result:
                # Include search results in the context
                enhanced_context = f"用户问题：{text}\n\n搜索结果：{search_result}\n\n请根据搜索结果回答用户问题。"
                if self.logger:
                    self.logger.debug(f"Generating response with search context: {enhanced_context[:200]}...")
                user_context = enhanced_context
            else:
                user_context = text
                if self.logger:
                    self.logger.debug(f"Generating response for text: {text[:100]}...")
            
            # Direct async streaming - no thread pool needed
            chunk_count = 0
            async for chunk in self.llm_provider.generate_stream(
                system_prompt=self.system_prompt,
                user_context=user_context
            ):
                chunk_count += 1
                if self.logger:
                    self.logger.debug(f"LLM text output chunk {chunk_count}: {chunk[:100]}{'...' if len(chunk) > 100 else ''}")
                yield chunk
                
            if self.logger:
                self.logger.debug(f"Text response completed - Total chunks: {chunk_count}")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Text response streaming error: {e}")
            yield f"抱歉，我暂时不能回答你的问题呢。"
    
    async def _generate_default_response_stream(self):
        """Generate default response"""
        response = "不好意思，我没有听清你说的，能再说一遍吗？"
        if self.logger:
            self.logger.debug(f"Generating default response: {response}")
        yield response
    
    async def _publish_llm_stream_event(self, response_generator, state: FastReplyState):
        """Publish LLM streaming response event for AgentEventService to handle TTS"""
        if self.logger:
            self.logger.debug("Publishing LLM streaming response event")
        
        accumulated_response = ""
        chunk_count = 0
        
        try:
            # Publish LLM streaming event with voice config
            llm_stream_event = Event(
                id=f"llm_stream_{state.get('session_id')}_{datetime.now().timestamp()}",
                type="llm_stream",
                data={
                    "stream_generator": response_generator,
                    "voice_config": self.voice_config,
                    "session_id": state.get('session_id')
                },
                timestamp=datetime.now(),
                session_id=state.get('session_id'),
                conversation_state=None,
                thread_id=None
            )
            
            # Publish to event bus for AgentEventService to handle
            if self.event_bus:
                await self.event_bus.publish(llm_stream_event)
            
            # Consume the response generator to get the final response
            async for chunk in response_generator:
                chunk_count += 1
                
                # Check for interruption
                if self._check_interruption_flag(state):
                    if self.logger:
                        self.logger.info(f"Streaming interrupted after {chunk_count} chunks")
                    break
                
                accumulated_response += chunk
                
                if self.logger and chunk_count % 5 == 0:
                    self.logger.debug(f"Processing chunk {chunk_count}: '{chunk[:20]}...', total: {len(accumulated_response)}")
                
                if self.connection_closed:
                    break
            
            # Update conversation state
            if accumulated_response and not self.connection_closed:
                ai_message = AIMessage(content=accumulated_response)
                state["messages"] = [ai_message]
                if self.logger:
                    self.logger.info(f"LLM streaming completed: {chunk_count} chunks, {len(accumulated_response)} chars")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in LLM streaming event: {e}")
    
    def _check_interruption_flag(self, state: FastReplyState) -> bool:
        """Check if interruption flag is set or connection is closed"""
        # Check for normal interruption
        if state.get("should_interrupt", False):
            return True
            
        # Check if connection is closed
        if self.connection_closed:
            if self.logger:
                self.logger.debug("Connection closed - interrupting response")
            return True
            
        # Check if connection handler indicates closed connection
        if (hasattr(self.connection_handler, 'websocket') and 
            hasattr(self.connection_handler.websocket, 'state')):
            from websockets.protocol import State
            if self.connection_handler.websocket.state in (State.CLOSING, State.CLOSED):
                if self.logger:
                    self.logger.debug("WebSocket connection closed - interrupting response")
                self.connection_closed = True
                return True
                
        return False
    
    
    async def _cancel_llm_stream(self):
        """Cancel ongoing LLM streaming if supported"""
        try:
            if hasattr(self.llm_provider, 'cancel_stream'):
                await self.llm_provider.cancel_stream()
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not cancel LLM stream: {e}")
    
    async def process_audio_event(self, audio_file: str, session_id: str) -> Dict[str, Any]:
        """Process audio input through LangGraph workflow"""
        if self.logger:
            self.logger.info(f"Processing audio event for session {session_id}")
            self.logger.debug(f"Audio file: {audio_file}")
        
        initial_state = FastReplyState(
            messages=[],
            audio_file=audio_file,
            session_id=session_id,
            is_speaking=False,
            should_interrupt=False,
            search_result=None,
            response_ready=False
        )
        
        try:
            if self.logger:
                self.logger.debug("Starting audio workflow execution")
            final_state = await self.graph.ainvoke(initial_state)
            if self.logger:
                self.logger.info(f"Audio workflow completed for session {session_id}")
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in FastReplyAgent audio workflow: {e}")
            return initial_state
    
    async def process_text_event(self, text: str, session_id: str) -> Dict[str, Any]:
        """Process text input through LangGraph workflow"""
        if self.logger:
            self.logger.info(f"Processing text event for session {session_id}")
            self.logger.debug(f"Text input: {text[:100]}...")
        
        human_message = HumanMessage(content=text)
        
        initial_state = FastReplyState(
            messages=[human_message],
            audio_file=None,
            session_id=session_id,
            is_speaking=False,
            should_interrupt=False,  # Changed from True to False for consistent behavior
            search_result=None,
            response_ready=False
        )
        
        try:
            if self.logger:
                self.logger.debug("Starting text workflow execution")
            final_state = await self.graph.ainvoke(initial_state)
            if self.logger:
                self.logger.info(f"Text workflow completed for session {session_id}")
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in FastReplyAgent text workflow: {e}")
            return initial_state
    
    def set_connection_closed(self):
        """Mark connection as closed to stop all operations"""
        self.connection_closed = True
        if self.logger:
            self.logger.debug("FastReplyAgent marked connection as closed")
    
    async def handle_interrupt(self, session_id: str):
        """Handle interruption signal from VadAgent"""
        if self.logger:
            self.logger.info(f"FastReplyAgent received VadAgent interrupt for session {session_id}")
            self.logger.debug("Setting interruption flag to stop current response")
        
        # In a real implementation, this would update the agent's state
        # to stop current processing immediately
        self.is_speaking = False
        
        # Signal to stop streaming if currently active
        try:
            await self._cancel_llm_stream()
            if self.logger:
                self.logger.debug("LLM streaming cancelled due to interrupt")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not cancel LLM stream on interrupt: {e}")
    
    async def update_with_search_result(self, search_result: str, session_id: str):
        """Update agent with search results from SearchAgent"""
        if self.logger:
            self.logger.info(f"FastReplyAgent received search result for session {session_id}")
            self.logger.debug(f"Search result content: {search_result[:200]}...")
        
        # In the new workflow, search results are handled through state updates
        # The check_interruption_node will detect and handle search results
        # This method serves as a callback for external search agent integration
        try:
            # Notify that search results are available
            # This would typically update the current workflow state
            if self.logger:
                self.logger.debug("Search result processed and ready for integration")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error processing search result: {e}")
    
    # Event handlers implementing BaseAgent interface
    async def _handle_audio_event(self, event: Event) -> None:
        """Handle audio events with conversation state"""
        try:
            file_path = event.data.get("file_path")
            if file_path and event.conversation_state:
                # Process audio with conversation context
                await self.process_audio_event_with_state(
                    file_path, event.session_id, event.conversation_state, event.thread_id
                )
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in audio event handler: {e}")
    
    async def _handle_text_event(self, event: Event) -> None:
        """Handle text events with conversation state"""
        try:
            text = event.data.get("text")
            if text and event.conversation_state:
                # Process text with conversation context
                await self.process_text_event_with_state(
                    text, event.session_id, event.conversation_state, event.thread_id
                )
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in text event handler: {e}")
    
    async def _handle_search_result_event(self, event: Event) -> None:
        """Handle search result events"""
        if self.logger:
            self.logger.debug(f"Handling search result event for session {event.session_id}")
        
        try:
            result = event.data.get("result", "")
            if result:
                if self.logger:
                    self.logger.info(f"Processing search result: {result[:100]}...")
                await self.update_with_search_result(result, event.session_id)
            else:
                if self.logger:
                    self.logger.warning("Received empty search result")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in search result event handler: {e}")
    
    async def _handle_interruption_event(self, event: Event) -> None:
        """Handle interruption events from VadAgent"""
        if self.logger:
            self.logger.debug(f"Handling interruption event for session {event.session_id}")
        
        try:
            await self.handle_interrupt(event.session_id)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in interruption event handler: {e}")
    
    # New methods for conversation state handling
    async def process_audio_event_with_state(self, audio_file: str, session_id: str, 
                                           conversation_state: dict, thread_id: str) -> Dict[str, Any]:
        """Process audio with conversation state context"""
        if self.logger:
            self.logger.info(f"FastReplyAgent processing audio with conversation context for session {session_id}")
            self.logger.debug(f"Thread ID: {thread_id}, conversation state keys: {list(conversation_state.keys()) if conversation_state else 'None'}")
        
        # Use existing process_audio_event method but with conversation context
        return await self.process_audio_event(audio_file, session_id)
    
    async def process_text_event_with_state(self, text: str, session_id: str, 
                                          conversation_state: dict, thread_id: str) -> Dict[str, Any]:
        """Process text with conversation state context"""
        if self.logger:
            self.logger.info(f"FastReplyAgent processing text with conversation context for session {session_id}")
            self.logger.debug(f"Thread ID: {thread_id}, text: {text[:50]}..., conversation state keys: {list(conversation_state.keys()) if conversation_state else 'None'}")
        
        # Use existing process_text_event method but with conversation context
        return await self.process_text_event(text, session_id)
    
    
    async def cleanup(self):
        """Clean up resources"""
        # All operations are now async, no thread pool to clean up
        if self.logger:
            self.logger.debug("FastReplyAgent cleanup completed")