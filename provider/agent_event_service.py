import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from langchain_core.messages import BaseMessage, HumanMessage
from .base_service import BaseService
from .audio_frontend.vad import SileroVAD
from .audio_frontend.audio_file_utils import AudioFileProcessor
from .agents.fast_reply_agent import FastReplyAgent
from .agents.search_agent import SearchAgent
from .agents.vad_agent import VadAgent
from .memory import ConversationState, ConversationMemoryManager, Event
from .tts.doubao_tts import DoubaoTTS

# 基于 asyncio 实现的事件总线
class EventBus:
    """Event bus for inter-agent communication"""
    
    def __init__(self):
        self.subscribers = {}
        self.logger = None
    
    def set_logger(self, logger):
        self.logger = logger
    
    def subscribe(self, event_type: str, callback):
        """Subscribe to an event type"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)
    
    async def publish(self, event: Event):
        """Publish an event to all subscribers"""
        if self.logger:
            self.logger.debug(f"Publishing event: {event.type} - {event.id}")
        
        if event.type in self.subscribers:
            tasks = []
            for callback in self.subscribers[event.type]:
                tasks.append(asyncio.create_task(callback(event)))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)


class AgentEventService(BaseService):
    """Multi-agent service with LangGraph-based agent architecture and native memory management"""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        
        # Initialize event bus
        self.event_bus = EventBus()
        self.event_bus.set_logger(self.logger)
        
        # Initialize LangGraph-native conversation memory manager
        self.memory_manager: Optional[ConversationMemoryManager] = None
        self.thread_id: Optional[str] = None
        self.config = config  # Store config for memory manager
        
        # Connection state tracking
        self.connection_closed = False
        
        # Initialize VAD and audio processor
        self.vad = SileroVAD(config)
        audio_config = config.get('websocket_audio', {})
        output_dir = audio_config.get('output_dir', 'log/audio')
        self.audio_saver = AudioFileProcessor(output_dir)
        
        # Initialize LangGraph agents with event bus
        self.fast_reply_agent = FastReplyAgent(config, connection_handler, self.event_bus)
        self.search_agent = SearchAgent(config, self.event_bus)
        self.vad_agent = VadAgent(config, self.event_bus)
        
        # Initialize TTS provider
        self.tts_provider = None
        self._init_tts_provider(config)
        
        # Set individual loggers for all agents
        from logger import get_logger
        self.fast_reply_agent.set_logger(get_logger('FastReplyAgent'))
        self.search_agent.set_logger(get_logger('SearchAgent'))
        self.vad_agent.set_logger(get_logger('VadAgent'))

        # Subscribe agents to events
        self._setup_event_subscriptions()
    
    def _init_tts_provider(self, config: Dict[str, Any]):
        """Initialize TTS provider"""
        try:
            tts_config = config.get('tts', {})
            tts_provider_type = tts_config.get('provider', 'doubao')
            
            if tts_provider_type == 'doubao':
                # Let DoubaoTTS handle its own configuration parsing
                self.tts_provider = DoubaoTTS(config)
                self.logger.info("Doubao TTS provider initialized successfully")
            else:
                self.logger.critical(f"Unsupported TTS provider: {tts_provider_type}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS provider: {e}")
            self.tts_provider = None
    
    def _init_memory_manager(self):
        """Initialize LangGraph-native memory manager for the session"""
        if self.session_id and not self.memory_manager:
            self.memory_manager = ConversationMemoryManager(
                session_id=self.session_id,
                config=self.config
            )
            self.thread_id = f"thread_{self.session_id}"
            self.logger.info(f"Initialized LangGraph memory manager for session: {self.session_id}")
    
    def _ensure_memory_manager(self):
        """Ensure memory manager is initialized"""
        if not self.memory_manager:
            self._init_memory_manager()

    def _setup_event_subscriptions(self):
        """Setup event subscriptions for all agents using BaseAgent interface"""
        # Each agent subscribes to its own interested events
        self.fast_reply_agent.subscribe_to_events(self.event_bus, self.session_id)
        # self.search_agent.subscribe_to_events(self.event_bus, self.session_id)
        # self.vad_agent.subscribe_to_events(self.event_bus, self.session_id)
        
        # Subscribe to LLM streaming events for TTS processing
        self.event_bus.subscribe("llm_stream", self._handle_llm_stream_event)

        self.logger.info("Event subscriptions setup completed.")


    async def initialize(self) -> bool:
        """Initialize the agent event service and all LangGraph agents with memory management"""
        try:
            # Initialize LangGraph-native memory manager when session starts
            self._init_memory_manager()
            
            # Initialize all LangGraph agents
            success = await self.fast_reply_agent.initialize()
            success &= await self.search_agent.initialize()
            success &= await self.vad_agent.initialize()
            
            if success:
                self.logger.info("AgentEventService initialized successfully with LangGraph agents and memory management")
            else:
                self.logger.error("Failed to initialize some LangGraph agents")
            
            return success
            
        except Exception as e:
            self.logger.error(f"AgentEventService initialization error: {e}")
            return False
    

    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Process audio data with VAD and event-based multi-agent handling"""
        # Skip processing if connection is closed
        if self.connection_closed:
            self.logger.debug("Skipping audio input processing - connection closed")
            return
            
        # Use VAD to detect speech end
        vad_result = self.vad.process_audio_frame(audio_data)

        if vad_result['speech_end']:
            # Speech ended, save audio and publish event
            opus_packets = vad_result['audio_buffer']
            await self._process_audio_packets_with_events(opus_packets, "Speech ended")
    
    async def handle_text_input(self, text: str) -> None:
        """Handle text input through LangGraph agents with native memory management"""
        # Skip processing if connection is closed
        if self.connection_closed:
            self.logger.debug(f"Skipping text input processing - connection closed: {text}")
            return
            
        self.logger.info(f"Received text input: {text}")
        
        try:
            self._ensure_memory_manager()
            
            # Add user message to conversation state using LangGraph mechanisms
            conversation_state = self.memory_manager.add_user_message(text)
            self.memory_manager.update_processing_status(True)
            
            # Create text event with LangGraph conversation state
            text_event = Event(
                id=f"text_{self.session_id}_{datetime.now().timestamp()}",
                type="text",
                data={"text": text},
                timestamp=datetime.now(),
                session_id=self.session_id,
                conversation_state=conversation_state,
                thread_id=self.thread_id
            )
            
            # Publish event to event bus for all agents to consume
            await self.event_bus.publish(text_event)
            
        except Exception as e:
            self.logger.error(f"Error handling text input: {e}")
    
    async def handle_client_audio_complete(self) -> None:
        """Handle client audio complete signal"""
        # Skip processing if connection is closed
        if self.connection_closed:
            self.logger.debug("Skipping audio complete processing - connection closed")
            return
            
        if self.vad.audio_packets and len(self.vad.audio_packets) > 10:
            await self._process_audio_packets_with_events(self.vad.audio_packets, "Client audio complete")
            self.vad.reset()
        else:
            self.logger.warning("Client audio complete called but no audio data available")
    
    async def _process_audio_packets_with_events(self, opus_packets, log_prefix: str) -> None:
        """Process audio packets and publish events for agents to consume with LangGraph memory"""
        # Skip processing if connection is closed
        if self.connection_closed:
            self.logger.debug(f"{log_prefix} - skipping audio processing - connection closed")
            return
            
        if not opus_packets or len(opus_packets) <= 10:
            self.logger.warning(f"{log_prefix} - speech too short, ignoring")
            return

        self.logger.info(f"{log_prefix} - processing {len(opus_packets)} Opus packets")

        # Save as MP3 file
        mp3_file = self.audio_saver.save_audio_as_mp3(opus_packets, self.session_id)
        if mp3_file:
            self.logger.info(f"{log_prefix} - audio saved to: {mp3_file}")

            try:
                # Double-check connection status before processing
                if self.connection_closed:
                    self.logger.debug(f"{log_prefix} - connection closed during audio processing, discarding")
                    return
                    
                self._ensure_memory_manager()
                
                # Add placeholder user message (will be updated after transcription)
                conversation_state = self.memory_manager.add_user_message(
                    content="[Audio Input - Transcribing...]",
                    audio_file=mp3_file
                )
                self.memory_manager.update_processing_status(True)
                
                # Create and publish audio event with LangGraph conversation state
                audio_event = Event(
                    id=f"audio_{self.session_id}_{datetime.now().timestamp()}",
                    type="audio",
                    data={
                        "file_path": mp3_file,
                        "log_prefix": log_prefix
                    },
                    timestamp=datetime.now(),
                    session_id=self.session_id,
                    conversation_state=conversation_state,
                    thread_id=self.thread_id
                )

                # Publish event to event bus for all agents to consume
                await self.event_bus.publish(audio_event)

            except Exception as e:
                self.logger.error(f"Error publishing audio event: {e}")
        else:
            self.logger.error(f"{log_prefix} - failed to save MP3 file")
    
    def update_conversation_with_transcription(self, transcription: str):
        """Update current conversation state with transcription using LangGraph memory"""
        if self.memory_manager:
            self.memory_manager.update_last_user_message(transcription)
            self.logger.info(f"Updated conversation state with transcription: {transcription}")
    
    def update_conversation_with_response(self, response: str):
        """Update conversation state with agent response using LangGraph memory"""
        if self.memory_manager:
            conversation_state = self.memory_manager.add_ai_message(response)
            self.memory_manager.update_processing_status(False)
            self.logger.info(f"Added AI response to conversation state: {response[:100]}...")
            return conversation_state
    
    def get_conversation_context_for_llm(self, max_tokens: Optional[int] = None) -> List[BaseMessage]:
        """Get properly formatted conversation context for LLM consumption"""
        if not self.memory_manager:
            return []
        
        return self.memory_manager.get_messages_for_llm(max_tokens=max_tokens)
    
    async def send_tts_response(self, text: str) -> bool:
        """
        Send TTS response to client (for complete text)
        
        Use this method when you have a complete sentence/response ready.
        For LLM streaming output, use send_streaming_tts_response() instead.
        
        Args:
            text: Complete text to convert to speech and send to client
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not text or not text.strip():
            self.logger.warning("Empty text provided for TTS response")
            return False
        
        if not self.tts_provider:
            self.logger.warning("TTS provider not available, sending text-only response")
            # Fallback to text-only response
            try:
                await self.connection_handler._send_text_tts_sequence(text)
                return True
            except Exception as e:
                self.logger.error(f"Failed to send text-only response: {e}")
                return False
        
        if not self.connection_handler:
            self.logger.error("No connection handler available for TTS response")
            return False
        
        self.logger.info(f"Generating TTS for text: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        try:
            # Generate TTS audio chunks and send them to client
            audio_chunks = []
            async for audio_chunk in self.tts_provider.text_to_speech_stream(text):
                if self.connection_closed:
                    self.logger.info("Connection closed during TTS generation, stopping")
                    return False
                
                audio_chunks.append(audio_chunk)
            
            if audio_chunks:
                # Combine all audio chunks
                combined_audio = b''.join(audio_chunks)
                
                # Send audio to client using the connection handler's method
                await self.connection_handler._send_audio_data(combined_audio, text)
                
                self.logger.info(f"TTS response sent successfully ({len(combined_audio)} bytes)")
                return True
            else:
                self.logger.warning("No audio data generated from TTS")
                # Fallback to text-only response
                await self.connection_handler._send_text_tts_sequence(text)
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to generate or send TTS response: {e}")
            # Fallback to text-only response
            try:
                await self.connection_handler._send_text_tts_sequence(text)
                return True
            except Exception as fallback_error:
                self.logger.error(f"Fallback text response also failed: {fallback_error}")
                return False
    
    async def send_realtime_tts_response(self, text_stream) -> bool:
        """
        Send realtime TTS response for LLM streaming chunks
        
        This method processes text chunks in real-time as they arrive from LLM streaming,
        providing the lowest possible latency between text generation and audio output.
        
        Args:
            text_stream: Async generator yielding text chunks from LLM
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.tts_provider:
            self.logger.warning("TTS provider not available, using text-only streaming")
            try:
                async for chunk in text_stream:
                    if self.connection_closed:
                        break
                    await self.connection_handler._send_text_tts_sequence(chunk)
                return True
            except Exception as e:
                self.logger.error(f"Failed to send text-only streaming: {e}")
                return False
        
        if not self.connection_handler:
            self.logger.error("No connection handler available for realtime TTS")
            return False
        
        self.logger.info("Starting realtime TTS streaming")
        
        try:
            # Create TTS session for real-time streaming
            async with self.tts_provider as tts:
                # Start concurrent audio handling
                audio_task = asyncio.create_task(
                    self._handle_realtime_audio_stream(tts)
                )
                
                chunk_count = 0
                try:
                    # Process LLM chunks in real-time
                    async for chunk in text_stream:
                        if self.connection_closed:
                            self.logger.info("Connection closed during realtime TTS, stopping")
                            break
                        
                        chunk_count += 1
                        
                        # Send text to client immediately for display
                        await self.connection_handler._send_text_tts_sequence(chunk)
                        
                        # Send chunk to TTS for immediate audio synthesis
                        await tts.send_text_segment(chunk)
                        
                        self.logger.debug(f"Processed realtime chunk {chunk_count}: '{chunk}'")
                    
                    # Finish TTS session
                    await tts._finish_session(tts.ws, tts.session_id)
                    self.logger.debug("TTS session finished - no more chunks")
                    
                    # Wait for audio streaming to complete
                    await audio_task
                    
                    self.logger.info(f"Realtime TTS completed: {chunk_count} chunks processed")
                    return True
                    
                except Exception as e:
                    self.logger.error(f"Error in realtime TTS processing: {e}")
                    audio_task.cancel()
                    return False
                
        except Exception as e:
            self.logger.error(f"Failed to initialize realtime TTS: {e}")
            return False
    
    async def _handle_realtime_audio_stream(self, tts):
        """Handle real-time audio stream from TTS"""
        try:
            audio_chunk_count = 0
            async for audio_chunk in tts.get_audio_stream():
                if self.connection_closed:
                    self.logger.info("Connection closed during audio streaming")
                    break
                
                audio_chunk_count += 1
                
                try:
                    await self.connection_handler._send_audio_chunk(audio_chunk)
                    if audio_chunk_count % 10 == 0:
                        self.logger.debug(f"Sent {audio_chunk_count} realtime audio chunks")
                except Exception as e:
                    self.logger.warning(f"Failed to send audio chunk {audio_chunk_count}: {e}")
                    break
            
            self.logger.info(f"Realtime audio streaming completed: {audio_chunk_count} chunks")
            
        except Exception as e:
            self.logger.error(f"Error in realtime audio streaming: {e}")
    
    async def _handle_llm_stream_event(self, event: Event) -> None:
        """Handle LLM streaming events from agents and process with TTS"""
        if self.logger:
            self.logger.debug(f"Handling LLM stream event for session {event.session_id}")
        
        try:
            stream_generator = event.data.get("stream_generator")
            voice_config = event.data.get("voice_config")
            
            if not stream_generator:
                self.logger.warning("No stream generator in LLM stream event")
                return
            
            # Update TTS speaker configuration if provided
            if voice_config and self.tts_provider:
                try:
                    # Create a voice-specific config by overriding the speaker
                    voice_config_dict = self.config.copy()
                    if 'tts' not in voice_config_dict:
                        voice_config_dict['tts'] = {}
                    voice_config_dict['tts']['speaker'] = voice_config
                    
                    # Create TTS with specific voice configuration
                    voice_tts_provider = DoubaoTTS(voice_config_dict)
                    await self._process_llm_stream_with_tts(stream_generator, voice_tts_provider)
                    return
                    
                except Exception as e:
                    self.logger.warning(f"Failed to create voice-specific TTS, using default: {e}")
            
            # Use default TTS provider
            await self._process_llm_stream_with_tts(stream_generator, self.tts_provider)
            
        except Exception as e:
            self.logger.error(f"Error handling LLM stream event: {e}")
    
    async def _process_llm_stream_with_tts(self, stream_generator, tts_provider):
        """Process LLM stream with realtime TTS"""
        if not tts_provider:
            self.logger.warning("No TTS provider available, using text-only streaming")
            try:
                async for chunk in stream_generator:
                    if self.connection_closed:
                        break
                    await self.connection_handler._send_text_tts_sequence(chunk)
                return
            except Exception as e:
                self.logger.error(f"Failed to send text-only streaming: {e}")
                return
        
        self.logger.info("Processing LLM stream with realtime TTS")
        
        try:
            # Create TTS session for real-time streaming
            async with tts_provider as tts:
                # Start concurrent audio handling
                audio_task = asyncio.create_task(
                    self._handle_realtime_audio_stream(tts)
                )
                
                chunk_count = 0
                try:
                    # Process LLM chunks in real-time
                    async for chunk in stream_generator:
                        if self.connection_closed:
                            self.logger.info("Connection closed during LLM stream TTS, stopping")
                            break
                        
                        chunk_count += 1
                        
                        # Send text to client immediately for display
                        await self.connection_handler._send_text_tts_sequence(chunk)
                        
                        # Send chunk to TTS for immediate audio synthesis
                        await tts.send_text_segment(chunk)
                        
                        if self.logger and chunk_count % 10 == 0:
                            self.logger.debug(f"Processed LLM stream chunk {chunk_count}: '{chunk[:20]}...'")
                    
                    # Finish TTS session
                    await tts._finish_session(tts.ws, tts.session_id)
                    self.logger.debug("LLM stream TTS session finished")
                    
                    # Wait for audio streaming to complete
                    await audio_task
                    
                    self.logger.info(f"LLM stream TTS completed: {chunk_count} chunks processed")
                    
                except Exception as e:
                    self.logger.error(f"Error in LLM stream TTS processing: {e}")
                    audio_task.cancel()
                
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM stream TTS: {e}")


    async def handle_connection_closed(self) -> None:
        """Handle WebSocket connection closed - stop all ongoing operations"""
        self.logger.info(f"Connection closed for session {self.session_id} - stopping all agents")
        
        # Set connection closed flag to stop ongoing operations
        self.connection_closed = True
        
        # Notify FastReplyAgent of connection closure
        try:
            if hasattr(self.fast_reply_agent, 'set_connection_closed'):
                self.fast_reply_agent.set_connection_closed()
                self.logger.debug("FastReplyAgent notified of connection closure")
        except Exception as e:
            self.logger.debug(f"Error notifying FastReplyAgent of closure: {e}")
        
        # Cancel any ongoing LLM streams in all agents
        try:
            if hasattr(self.fast_reply_agent, '_cancel_llm_stream'):
                await self.fast_reply_agent._cancel_llm_stream()
                self.logger.debug("FastReplyAgent LLM stream cancelled")
        except Exception as e:
            self.logger.debug(f"Error cancelling FastReplyAgent stream: {e}")
            
        try:
            if hasattr(self.search_agent, 'gemini_provider') and hasattr(self.search_agent.gemini_provider, 'cancel'):
                self.search_agent.gemini_provider.cancel()
                self.logger.debug("SearchAgent LLM stream cancelled")
        except Exception as e:
            self.logger.debug(f"Error cancelling SearchAgent stream: {e}")
        
        # Reset VAD to discard any buffered audio
        self.vad.reset()
        
        self.logger.info("All agents notified of connection closure")
    
    async def cleanup(self) -> None:
        """Clean up resources"""
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        
        self.vad.reset()
        self.logger.info("AgentEventService cleaned up resources")