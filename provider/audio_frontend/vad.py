import time
import numpy as np
import torch
from pathlib import Path
from typing import Dict, Any
from logger import get_logger

logger = get_logger(__name__)

class SileroVAD:
    """基于Silero VAD模型的语音活动检测器"""
    
    def __init__(self, config: Dict[str, Any] = None, volume_threshold=0.5, silence_duration_ms=1500, max_speech_duration_ms=15000):
        # 从config读取VAD参数
        if config and 'websocket_vad' in config:
            vad_config = config['websocket_vad']
            self.vad_threshold = float(vad_config.get('threshold', volume_threshold))
            self.silence_duration_ms = int(vad_config.get('silence_duration_ms', silence_duration_ms))
            self.max_speech_duration_ms = int(vad_config.get('max_speech_duration_ms', max_speech_duration_ms))
        else:
            self.vad_threshold = volume_threshold
            self.silence_duration_ms = silence_duration_ms  
            self.max_speech_duration_ms = max_speech_duration_ms
            
        logger.info(f"[SILERO VAD] Using threshold: {self.vad_threshold}")
        logger.info(f"[SILERO VAD] Using silence_duration_ms: {self.silence_duration_ms}ms")
        logger.info(f"[SILERO VAD] Using max_speech_duration_ms: {self.max_speech_duration_ms}ms")
        
        # 初始化Silero VAD模型
        try:
            # 使用相对路径，从当前文件所在目录找到模型目录
            current_dir = Path(__file__).parent
            model_dir = current_dir / "snakers4_silero-vad"
            self.model, self.utils = torch.hub.load(
                repo_or_dir=str(model_dir),
                source="local", 
                model="silero_vad",
                force_reload=False,
            )
            logger.info("[SILERO VAD] Model loaded successfully")
        except Exception as e:
            logger.critical(f"[SILERO VAD] Failed to load model: {e}")
            raise
            
        # 初始化Opus解码器
        try:
            import opuslib_next
            self.decoder = opuslib_next.Decoder(16000, 1)  # 16kHz, 单声道
            logger.info("[SILERO VAD] Opus decoder initialized successfully")
        except Exception as e:
            logger.critical(f"[SILERO VAD] Failed to initialize Opus decoder: {e}")
            raise
            
        self.reset()
        
    def reset(self):
        """重置VAD状态"""
        self.has_voice = False
        self.last_voice_time = 0
        self.speech_start_time = 0
        self.audio_packets = []          # 存储原始Opus音频包列表
        self.pcm_buffer = bytearray()    # 存储解码后的PCM数据
        self.speech_detected = False
        self.frame_count = 0
        
    def process_audio_frame(self, opus_data: bytes) -> dict:
        """
        处理Opus音频帧，使用Silero VAD模型检测
        """
        self.frame_count += 1
        
        result = {
            'has_voice': False,
            'speech_end': False,
            'audio_buffer': None
        }
        
        # 解码Opus数据为PCM
        try:
            pcm_frame = self.decoder.decode(opus_data, 960)
            self.pcm_buffer.extend(pcm_frame)
            
            # 处理缓冲区中的完整帧（每次处理512采样点）
            has_voice_now = False
            while len(self.pcm_buffer) >= 512 * 2:  # 512采样点 * 2字节
                # 提取前512个采样点（1024字节）
                chunk = self.pcm_buffer[:512 * 2]
                self.pcm_buffer = self.pcm_buffer[512 * 2:]
                
                # 转换为模型需要的张量格式
                audio_int16 = np.frombuffer(chunk, dtype=np.int16)
                audio_float32 = audio_int16.astype(np.float32) / 32768.0
                audio_tensor = torch.from_numpy(audio_float32)
                
                # 使用Silero VAD模型检测语音活动
                with torch.no_grad():
                    speech_prob = self.model(audio_tensor, 16000).item()
                has_voice_now = speech_prob >= self.vad_threshold
                
                if has_voice_now:
                    break  # 只要有一块检测到语音就算有语音
                    
        except Exception as e:
            logger.debug(f"[SILERO VAD] Processing error: {e}")
            has_voice_now = False
        
        current_time = time.time() * 1000
        result['has_voice'] = has_voice_now
        
        # 每50帧记录一次VAD状态
        if self.frame_count % 50 == 0:
            logger.debug(f"[SILERO VAD] Frame {self.frame_count}: has_voice={has_voice_now}, speech_detected={self.speech_detected}")
        
        # 语音状态处理逻辑，参考原始Silero VAD实现
        if has_voice_now:
            if not self.has_voice:
                # 开始检测到语音
                logger.info(f"[SILERO VAD] Speech started at frame {self.frame_count}")
                self.speech_detected = True
                self.speech_start_time = current_time
            self.has_voice = True
            self.last_voice_time = current_time
            # 只在检测到语音时才累积包
            self.audio_packets.append(opus_data)
        else:
            # 如果之前有声音，但本次没有声音，且与上次有声音的时间差已经超过了静默阈值，则认为已经说完一句话
            if self.has_voice and self.speech_detected:
                # 在静默期间也累积一些包，但限制数量（只保留少量静默包用于自然过渡）
                silence_duration = current_time - self.last_voice_time
                if silence_duration < 300:  # 前300ms的静默仍然累积
                    self.audio_packets.append(opus_data)
                
                if silence_duration >= self.silence_duration_ms:
                    # 语音结束
                    logger.info(f"[SILERO VAD] Speech ended at frame {self.frame_count} after {silence_duration:.0f}ms silence, packets: {len(self.audio_packets)}")
                    result['speech_end'] = True
                    result['audio_buffer'] = list(self.audio_packets)  # 返回Opus包列表
                    # 重置状态（保留解码器和模型）
                    decoder = self.decoder
                    model = self.model
                    utils = self.utils
                    self.reset()
                    self.decoder = decoder
                    self.model = model
                    self.utils = utils
            elif not self.speech_detected:
                # 还没有检测到语音，不累积包
                pass
        
        # 检查是否需要强制截断（语音持续时间过长）
        if self.speech_detected and self.speech_start_time > 0:
            speech_duration = current_time - self.speech_start_time
            if speech_duration >= self.max_speech_duration_ms:
                # 强制截断
                logger.info(f"[SILERO VAD] Force speech segmentation at frame {self.frame_count} after {speech_duration:.0f}ms continuous speech, packets: {len(self.audio_packets)}")
                result['speech_end'] = True
                result['audio_buffer'] = list(self.audio_packets)  # 返回Opus包列表
                # 重置状态（保留解码器和模型）
                decoder = self.decoder
                model = self.model
                utils = self.utils
                self.reset()
                self.decoder = decoder
                self.model = model
                self.utils = utils
                    
        return result