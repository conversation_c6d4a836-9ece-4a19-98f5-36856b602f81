import asyncio
from typing import Dict, Any
from .base_service import BaseService
from .audio_frontend.vad import SileroVAD
from .audio_frontend.audio_file_utils import AudioFileProcessor
from .llm import llm_factory

class EchoService(BaseService):
    """Echo service that returns user's audio back after they finish speaking."""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        self.vad = SileroVAD(config)
        # Initialize audio saver with output directory from config or use default
        audio_config = config.get('websocket_audio', {})
        output_dir = audio_config.get('output_dir', 'log/audio')
        self.audio_saver = AudioFileProcessor(output_dir)
        # Initialize LLM provider using factory
        try:
            self.llm_provider = llm_factory.create_llm_provider(config, 'echo')
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM provider: {e}")
            raise
        
    async def initialize(self) -> bool:
        """Initialize the echo service"""
        self.logger.info("Initialized")
        return True
        
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Process audio data with VAD detection."""
        # 使用VAD检测语音活动
        vad_result = self.vad.process_audio_frame(audio_data)
        
        if vad_result['speech_end']:
            # 检测到语音结束，处理音频
            opus_packets = vad_result['audio_buffer']
            await self._process_audio_packets(opus_packets, "Speech ended")

    async def handle_text_input(self, text: str) -> None:
        self.logger.info(f"Echoing back text: {text}")
        await self.connection_handler._send_text_tts_sequence(text)

    async def handle_client_audio_complete(self) -> None:
        """Handle client audio complete - force completion of current audio session when client indicates they finished speaking."""
        # Check if VAD has collected audio packets
        if self.vad.audio_packets and len(self.vad.audio_packets) > 10:
            await self._process_audio_packets(self.vad.audio_packets, "Client audio complete")
            # Reset VAD state after processing
            self.vad.reset()
        else:
            self.logger.warning("Client audio complete called but no audio data available from VAD")

    async def _process_audio_packets(self, opus_packets, log_prefix: str) -> None:
        """Common logic for processing audio packets."""
        if not opus_packets or len(opus_packets) <= 10:
            self.logger.warning(f"{log_prefix} - speech too short, ignoring")
            return
            
        self.logger.info(f"{log_prefix} - processing {len(opus_packets)} Opus packets")
        
        # Save as MP3 file
        mp3_file = self.audio_saver.save_audio_as_mp3(opus_packets, self.session_id)
        if mp3_file:
            self.logger.info(f"{log_prefix} - audio saved to: {mp3_file}")

            # 创建后台任务处理LLM分析和音频发送，避免阻塞事件循环
            asyncio.create_task(self._analyze_and_send_audio(mp3_file, log_prefix))
        else:
            self.logger.error(f"{log_prefix} - failed to save MP3 file, cannot send echo")

    async def _analyze_and_send_audio(self, audio_file: str, log_prefix: str) -> None:
        """在后台异步分析音频并发送结果，不阻塞WebSocket事件循环"""
        try:
            # Analyze audio with LLM if available to get text (这里可能会花费较长时间)
            if self.llm_provider:
                # 使用线程池执行同步的LLM分析，避免阻塞事件循环
                analysis_text = await asyncio.to_thread(self._analyze_audio_get_text_sync, audio_file)
            else:
                analysis_text = "（回声）"

            # Send audio echo from audio file with analysis text
            await self.connection_handler._send_audio_from_file(audio_file, analysis_text)
            
        except Exception as e:
            self.logger.error(f"{log_prefix} - error in background audio analysis: {e}")
            # 如果LLM分析失败，仍然发送原音频
            try:
                await self.connection_handler._send_audio_from_file(audio_file, "（回声）")
            except Exception as send_error:
                self.logger.error(f"{log_prefix} - failed to send fallback audio: {send_error}")

    def _analyze_audio_get_text_sync(self, audio_file_path: str) -> str:
        """同步版本的音频分析，用于在线程池中执行（支持MP3和WAV格式）"""
        try:
            system_prompt = """分析这段语音内容，请回答以下问题：
1. 有几个人在说话？
2. 能听出是在什么环境（例如办公室、大街上、机场、海边等）下说话的吗？ 
3. 每个人分别在说什么？情绪如何？

格式如下：
说话人数：X人
环境：描述环境
说话人1：（情绪）语音转录的文本
说话人2：（情绪）语音转录的文本
..."""

            user_context = "请分析这段音频"
            
            # 使用同步方法调用LLM provider（在线程池中运行，不会阻塞事件循环）
            # 注意：这里需要创建新的事件循环，因为我们在线程池中
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response_text = loop.run_until_complete(
                    self.llm_provider.generate(
                        system_prompt=system_prompt,
                        user_context=user_context,
                        file_path=audio_file_path
                    )
                )
            finally:
                loop.close()
            
            if response_text and response_text.strip():
                self.logger.info(f"LLM audio analysis completed")
                return response_text.strip()
            else:
                self.logger.error("LLM returned empty response")
                return "抱歉，语音分析失败了"

        except Exception as e:
            self.logger.error(f"Error in audio analysis: {str(e)}")
            return "抱歉，语音分析出现错误"


    async def cleanup(self) -> None:
        """Clean up resources and end monitoring session."""
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        self.vad.reset()
        self.logger.info("Cleaned up resources.")

