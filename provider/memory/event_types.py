"""
Event data structures for the event bus with LangGraph conversation state support.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from .conversation_state import ConversationState


@dataclass
class Event:
    """Event data structure for the event bus"""
    id: str
    type: str  # 'audio', 'text', 'agent_message', 'interrupt'
    data: Any
    timestamp: datetime
    session_id: str
    metadata: Dict[str, Any] = None
    conversation_state: Optional[ConversationState] = None  # LangGraph conversation state
    thread_id: Optional[str] = None  # LangGraph thread ID for persistence

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        # Convert conversation_state to dict if present
        if self.conversation_state:
            result["conversation_state"] = dict(self.conversation_state)
        return result