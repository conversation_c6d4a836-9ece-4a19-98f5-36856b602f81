# Memory Management Module

This module provides LangGraph-native conversation memory management with sliding window and summarization capabilities.

## Components

### 1. ConversationState
LangGraph-native conversation state that extends `MessagesState`:
- Maintains message history with automatic `add_messages` reducer
- Tracks session metadata and processing status
- Supports sliding window configuration

### 2. ConversationMemoryManager
Core memory management class that handles:
- **Sliding Window**: Automatically keeps only recent N messages
- **Summarization**: Compresses older messages into summaries
- **Token Management**: Provides LLM-optimized message formatting
- **State Updates**: Thread-safe conversation state updates

### 3. Event
Event data structure for the event bus:
- Carries LangGraph conversation state
- Supports thread-based persistence
- Enables cross-agent communication

## Usage Examples

### Basic Usage
```python
from .memory import ConversationMemoryManager, Event

# Initialize memory manager with config
config = {
    'memory': {
        'window_size': 10,
        'max_tokens': 4000
    }
}
memory_manager = ConversationMemoryManager(
    session_id="user_123",
    config=config
)

# Or with defaults (window_size=10, max_tokens=4000)
memory_manager = ConversationMemoryManager(session_id="user_123")

# Add user message
state = memory_manager.add_user_message("Hello, how are you?")

# Add AI response
state = memory_manager.add_ai_message("I'm doing well, thank you!")

# Get messages for LLM (uses configured max_tokens by default)
messages = memory_manager.get_messages_for_llm()
```

### Sliding Window Behavior
When the conversation exceeds `window_size` messages:
1. Recent messages (last `window_size//2`) are kept
2. Older messages are summarized and stored in `state.summary`
3. Memory usage remains constant while preserving context

### Event-Driven Integration
```python
# Create event with conversation state
event = Event(
    id=f"text_{session_id}_{timestamp}",
    type="text",
    data={"text": user_input},
    timestamp=datetime.now(),
    session_id=session_id,
    conversation_state=memory_manager.state,
    thread_id=f"thread_{session_id}"
)

# Agents receive full conversation context
await event_bus.publish(event)
```

## Benefits

1. **LangGraph Native**: Uses official LangGraph patterns and types
2. **Memory Efficient**: Sliding window prevents unbounded growth
3. **Context Preserved**: Summarization maintains conversation context
4. **Token Aware**: Built-in token limiting for LLM consumption
5. **Thread Safe**: Designed for concurrent multi-agent access

## Configuration

Memory behavior can be configured via config:
```python
memory_config = {
    'window_size': 10,      # Number of messages to keep
    'max_tokens': 4000      # Token limit for LLM context
}
```