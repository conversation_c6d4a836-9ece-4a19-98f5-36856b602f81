"""
LangGraph-native conversation state definition with sliding window memory support.
"""

from typing import Dict, Any, Optional, TypedDict

try:
    from langgraph.graph import MessagesState
    _BaseState = MessagesState
except ImportError:
    # Fallback for environments without langgraph
    _BaseState = TypedDict


class ConversationState(_BaseState):
    """LangGraph-native conversation state with sliding window memory"""
    session_id: str
    summary: str = ""  # Summary of earlier conversation
    window_size: int = 10  # Keep last N messages in memory
    total_messages: int = 0  # Track total message count
    current_audio_file: Optional[str] = None
    is_processing: bool = False
    metadata: Dict[str, Any] = None