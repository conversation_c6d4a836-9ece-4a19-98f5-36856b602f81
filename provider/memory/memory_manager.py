"""
LangGraph-native conversation memory manager with sliding window and summarization.
"""

from typing import List, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langgraph.graph.message import add_messages
from langchain_core.messages.utils import trim_messages
from .conversation_state import ConversationState
    
class ConversationMemoryManager:
    """Manages conversation memory using LangGraph's native mechanisms"""
    
    def __init__(self, session_id: str, config: dict = None):
        self.session_id = session_id
        
        # Configure memory settings from config
        memory_config = config.get('memory', {}) if config else {}
        self.window_size = memory_config.get('window_size', 10)
        self.max_tokens = memory_config.get('max_tokens', 4000)
        
        self.state: ConversationState = {
            "messages": [],
            "session_id": session_id,
            "summary": "",
            "window_size": self.window_size,
            "total_messages": 0,
            "current_audio_file": None,
            "is_processing": False,
            "metadata": {}
        }
    
    def add_user_message(self, content: str, audio_file: Optional[str] = None) -> ConversationState:
        """Add user message to conversation state"""
        message = HumanMessage(content=content)
        if audio_file:
            message.additional_kwargs["audio_file"] = audio_file
        
        # Add message using LangGraph's add_messages reducer
        self.state["messages"] = add_messages(self.state["messages"], [message])
        self.state["total_messages"] += 1
        self.state["current_audio_file"] = audio_file
        
        # Apply sliding window with summarization if needed
        self._apply_sliding_window()
        
        return self.state
    
    def add_ai_message(self, content: str) -> ConversationState:
        """Add AI response to conversation state"""
        message = AIMessage(content=content)
        self.state["messages"] = add_messages(self.state["messages"], [message])
        self.state["total_messages"] += 1
        
        # Apply sliding window with summarization if needed
        self._apply_sliding_window()
        
        return self.state
    
    def _apply_sliding_window(self):
        """Apply sliding window memory management with summarization"""
        if len(self.state["messages"]) > self.window_size:
            # Get messages to summarize (all except the last window_size/2)
            keep_recent = self.window_size // 2
            messages_to_summarize = self.state["messages"][:-keep_recent]
            recent_messages = self.state["messages"][-keep_recent:]
            
            # Create summary of older messages
            if messages_to_summarize:
                summary_content = self._create_summary(messages_to_summarize)
                
                # Update summary with new content
                if self.state["summary"]:
                    self.state["summary"] = f"{self.state['summary']}\n\n{summary_content}"
                else:
                    self.state["summary"] = summary_content
            
            # Keep only recent messages
            self.state["messages"] = recent_messages
    
    def _create_summary(self, messages: List[BaseMessage]) -> str:
        """Create summary of messages for memory compression"""
        summary_parts = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                summary_parts.append(f"User: {msg.content}")
            elif isinstance(msg, AIMessage):
                summary_parts.append(f"Assistant: {msg.content}")
        
        return f"Previous conversation:\n" + "\n".join(summary_parts)
    
    def get_full_context(self) -> str:
        """Get full conversation context including summary and recent messages"""
        context_parts = []
        
        # Add summary if exists
        if self.state["summary"]:
            context_parts.append(self.state["summary"])
        
        # Add recent messages
        if self.state["messages"]:
            context_parts.append("\nRecent conversation:")
            for msg in self.state["messages"]:
                if isinstance(msg, HumanMessage):
                    context_parts.append(f"User: {msg.content}")
                elif isinstance(msg, AIMessage):
                    context_parts.append(f"Assistant: {msg.content}")
        
        return "\n".join(context_parts)
    
    def get_messages_for_llm(self, max_tokens: Optional[int] = None) -> List[BaseMessage]:
        """Get messages formatted for LLM with optional token limiting"""
        messages = self.state["messages"].copy()
        
        # Add system message with summary if exists
        if self.state["summary"]:
            system_msg = SystemMessage(content=f"Conversation context: {self.state['summary']}")
            messages = [system_msg] + messages
        
        # Apply token limiting if specified (use configured max_tokens as default)
        token_limit = max_tokens or self.max_tokens
        if token_limit:
            messages = trim_messages(
                messages,
                max_tokens=token_limit,
                strategy="last",
                token_counter=len  # Simple character count, replace with proper tokenizer
            )
        
        return messages
    
    def update_processing_status(self, is_processing: bool):
        """Update processing status"""
        self.state["is_processing"] = is_processing
    
    def update_last_user_message(self, new_content: str):
        """Update the content of the last user message (useful for transcription updates)"""
        if self.state["messages"] and isinstance(self.state["messages"][-1], HumanMessage):
            self.state["messages"][-1].content = new_content