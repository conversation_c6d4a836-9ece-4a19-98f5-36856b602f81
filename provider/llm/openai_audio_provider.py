import openai
import base64
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from .llm_base import LLMBase


class OpenAIAudioProvider(LLMBase):
    """OpenAI audio provider with streaming support for audio input processing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize OpenAI audio provider with configuration."""
        super().__init__(config)
        
        # Read directly from config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gpt-4o-audio-preview')
        
        if not self.api_key:
            raise ValueError("OpenAI API key not found in configuration")
        
        # Initialize OpenAI client
        self.client = openai.OpenAI(api_key=self.api_key)
        
        self.logger.info(f"OpenAI audio provider initialized with model: {self.model}")
    
    async def generate(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate non-streaming response from OpenAI with audio input support."""
        try:
            # DEBUG: Log input parameters
            self.logger.debug(f"Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Input - User context: {self._format_long_text(user_context)}")
            self.logger.debug(f"Input - File path: {file_path if file_path else 'None'}")
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # Prepare user message content
            user_content = []

            # Add text content
            if user_context:
                user_content.append({"type": "text", "text": user_context})

            # If file_path is provided, add audio input
            if file_path:
                try:
                    import os
                    
                    # Get file size for logging
                    file_size = os.path.getsize(file_path)
                    
                    with open(file_path, 'rb') as audio_file:
                        audio_data = audio_file.read()
                        audio_b64 = base64.b64encode(audio_data).decode('utf-8')

                    # Detect audio format from file extension
                    file_extension = os.path.splitext(file_path)[1].lower()
                    if file_extension == '.mp3':
                        audio_format = "mp3"
                    elif file_extension == '.wav':
                        audio_format = "wav"
                    else:
                        # Default to wav for unknown formats
                        audio_format = "wav"
                        self.logger.warning(f"Unknown audio format {file_extension}, defaulting to wav")

                    # DEBUG: Log audio file details
                    self.logger.debug(f"File processing - Path: {file_path}, Size: {file_size} bytes, Format: {audio_format}")
                    self.logger.debug(f"File processing - Base64 length: {len(audio_b64)} characters")

                    # Add audio input to user content
                    user_content.append({
                        "type": "input_audio",
                        "input_audio": {
                            "data": audio_b64,
                            "format": audio_format
                        }
                    })

                    self.logger.info(f"Audio file loaded for OpenAI analysis: {file_path} (format: {audio_format})")

                except Exception as e:
                    self.logger.error(f"Failed to load audio file: {str(e)}")
                    return f"音频文件加载失败：{str(e)}"

            # Add user message
            messages.append({
                "role": "user",
                "content": user_content if len(user_content) > 1 else user_context
            })

            # Determine if we need audio modalities
            modalities = ["text", "audio"] if file_path else ["text"]

            # DEBUG: Log request configuration
            self.logger.debug(f"Config - Model: {self.model}, Modalities: {modalities}")
            self.logger.debug(f"Config - Message count: {len(messages)}, User content parts: {len(user_content) if isinstance(user_content, list) else 1}")

            # Create non-streaming response
            response = self.client.chat.completions.create(
                model=self.model,
                modalities=modalities,
                audio={
                    "voice": "alloy",
                    "format": "mp3"
                },
                response_format={"type": "text"},
                messages=messages,
                temperature=0.3
            )
            
            # Get text response
            if response.choices[0].message.audio and response.choices[0].message.audio.transcript:
                response_text = response.choices[0].message.audio.transcript
                self.logger.debug(f"Output - Audio transcript: {self._format_long_text(response_text)}")
                return response_text
            else:
                response_text = response.choices[0].message.content or ""
                self.logger.debug(f"Output - Text response: {self._format_long_text(response_text)}")
                return response_text
                    
        except Exception as e:
            self.logger.error(f"Error in OpenAI generation: {str(e)}")
            return f"生成回复时出错：{str(e)}"

    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response from OpenAI with audio input support."""
        # DEBUG: Log streaming input
        self.logger.debug(f"Stream Input - System prompt: {self._format_long_text(system_prompt)}")
        self.logger.debug(f"Stream Input - User context: {self._format_long_text(user_context)}")
        self.logger.debug(f"Stream Input - File path: {file_path if file_path else 'None'}")
        
        # TODO: Implement streaming version - temporarily empty
        # For now, fall back to non-streaming
        result = await self.generate(system_prompt, user_context, file_path)
        self.logger.debug(f"Stream Output - Yielding response: {self._format_long_text(result)}")
        yield result
    
