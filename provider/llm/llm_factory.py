import importlib
from typing import Dict, Any
from .llm_base import LLMBase
import logging

logger = logging.getLogger(__name__)


class LLMFactory:
    """Factory class for creating LLM provider instances"""
    
    def create_llm_provider(self, config: Dict[str, Any], service_name: str = None) -> LLMBase:
        """
        Create an LLM provider based on configuration.
        
        Args:
            config: Configuration dictionary
            service_name: Name of the service/agent (e.g., 'fast_reply', 'search', 'echo')
                         If provided, uses service-specific config from llm.agents or llm.echo
            
        Returns:
            LLMBase instance
            
        Raises:
            ValueError: If provider not found or creation fails
        """
        if not service_name:
            raise ValueError("service_name is required for LLM provider creation")
            
        llm_config = config.get('llm', {})
        
        # Try to get configuration from agents section first, then direct llm section
        agents_config = llm_config.get('agents', {})
        service_config = agents_config.get(service_name)
        
        if not service_config:
            # Try to get from direct llm section (for services like echo)
            service_config = llm_config.get(service_name)
            
        if not service_config:
            raise ValueError(f"No LLM configuration found for service '{service_name}' in configuration")
        
        provider_class_name = service_config.get('provider')
        
        if not provider_class_name:
            raise ValueError(f"No LLM provider specified for service '{service_name}' in configuration")
        
        if provider_class_name is None:
            raise ValueError(f"Service '{service_name}' is configured with null provider (no LLM needed)")
        
        vendor = service_config.get('vendor')
        model = service_config.get('model')
        
        if not vendor:
            raise ValueError(f"No vendor specified for service '{service_name}' in configuration")
        
        try:
            # Get API key for the vendor
            api_keys = config.get('api_key', {})
            api_key = api_keys.get(vendor)
            
            if not api_key:
                raise ValueError(f"No API key found for vendor '{vendor}' in configuration")
            
            # Create simplified provider configuration
            provider_config = {
                'api_key': api_key,
                'model': model
            }
            
            # Try to get the class from current module's scope
            current_module = importlib.import_module('provider.llm')
            provider_class = getattr(current_module, provider_class_name)
            
            # Verify it's a subclass of LLMBase
            if not issubclass(provider_class, LLMBase):
                raise ValueError(f"{provider_class_name} is not a valid LLM provider (must inherit from LLMBase)")
            
            provider = provider_class(provider_config)
            
            # Log creation with appropriate context
            logger.info(f"Created LLM provider for service '{service_name}': {provider_class_name}")
            
            return provider
            
        except AttributeError:
            raise ValueError(f"Provider class '{provider_class_name}' not found")
        except Exception as e:
            raise ValueError(f"Failed to create provider {provider_class_name} for service '{service_name}': {e}")
    
    # Legacy methods for backward compatibility
    def create_agent_llm_provider(self, config: Dict[str, Any], agent_name: str) -> LLMBase:
        """Legacy method - use create_llm_provider instead"""
        return self.create_llm_provider(config, agent_name)
    
    def create_audio_llm_provider(self, config: Dict[str, Any]) -> LLMBase:
        """Legacy method - use create_llm_provider with service name instead"""
        raise ValueError("create_audio_llm_provider is deprecated. Use create_llm_provider(config, 'echo') instead.")

# Global factory instance
llm_factory = LLMFactory()