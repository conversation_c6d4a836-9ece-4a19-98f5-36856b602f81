from google import genai
from google.genai import types
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from .llm_base import LLMBase


class GeminiProvider(LLMBase):
    """Gemini provider with streaming support."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini provider with configuration."""
        super().__init__(config)
        
        # Read directly from config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gemini-2.5-flash')
        
        if not self.api_key:
            raise ValueError("Gemini API key not found in configuration")
        
        # Initialize Gemini client with timeout settings
        import httpx
        timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=10.0)
        self.client = genai.Client(api_key=self.api_key, http_options={'timeout': timeout})

        self.logger.info(f"Gemini provider initialized with model: {self.model}")
    
    async def generate(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate non-streaming response from Gemini."""
        try:
            # DEBUG: Log input parameters
            self.logger.debug(f"Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Input - User context: {self._format_long_text(user_context)}")
            self.logger.debug(f"Input - File path: {file_path if file_path else 'None'}")
            
            # Prepare content
            contents = []

            # If file_path is provided, upload it
            if file_path:
                try:
                    import os
                    
                    # Get file size for logging
                    file_size = os.path.getsize(file_path)
                    file_extension = os.path.splitext(file_path)[1].lower()
                    
                    self.logger.debug(f"File upload - Path: {file_path}, Size: {file_size} bytes, Format: {file_extension}")
                    
                    uploaded_file = self.client.files.upload(file=file_path)
                    contents = [user_context, uploaded_file]
                    
                    self.logger.info(f"Audio file uploaded for Gemini analysis: {file_path} (format: {file_extension})")
                    self.logger.debug(f"File upload successful - File ID: {uploaded_file.name if hasattr(uploaded_file, 'name') else 'unknown'}")
                except Exception as e:
                    self.logger.error(f"Failed to upload file to Gemini: {str(e)}")
                    return f"文件上传失败：{str(e)}"
            else:
                contents = user_context

            # Create generation config with system instruction
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # Disable thinking
            )

            # DEBUG: Log generation config
            self.logger.debug(f"Config - Model: {self.model}, System instruction enabled: {bool(system_prompt)}")

            # Create non-streaming response (using async client)
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            # DEBUG: Log output
            response_text = response.text
            self.logger.debug(f"Output - Response: {self._format_long_text(response_text)}")
            
            return response_text
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini generation: {str(e)}")
            return f"生成回复时出错：{str(e)}"

    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response from Gemini."""
        try:
            # DEBUG: Log streaming input
            self.logger.debug(f"Stream Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Stream Input - User context: {self._format_long_text(user_context)}")
            self.logger.debug(f"Stream Input - File path: {file_path if file_path else 'None'}")
            
            # Prepare content
            contents = []

            # If file_path is provided, upload it
            if file_path:
                try:
                    import os
                    
                    # Get file size for logging
                    file_size = os.path.getsize(file_path)
                    file_extension = os.path.splitext(file_path)[1].lower()
                    
                    self.logger.debug(f"Stream File upload - Path: {file_path}, Size: {file_size} bytes, Format: {file_extension}")
                    
                    uploaded_file = self.client.files.upload(file=file_path)
                    contents = [user_context, uploaded_file]
                    
                    # Get file format for logging
                    self.logger.info(f"Audio file uploaded for Gemini analysis: {file_path} (format: {file_extension})")
                    self.logger.debug(f"Stream File upload successful - File ID: {uploaded_file.name if hasattr(uploaded_file, 'name') else 'unknown'}")
                except Exception as e:
                    self.logger.error(f"Failed to upload file to Gemini: {str(e)}")
                    yield f"文件上传失败：{str(e)}"
                    return
            else:
                contents = user_context

            # Create generation config with system instruction
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # Disable thinking
            )

            # DEBUG: Log generation config
            self.logger.debug(f"Stream Config - Model: {self.model}, System instruction enabled: {bool(system_prompt)}")

            # Create streaming response (using async client)
            response = await self.client.aio.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            chunk_count = 0
            async for chunk in response:
                if self._cancelled:
                    self.logger.debug(f"Stream cancelled after {chunk_count} chunks")
                    break
                    
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    self.logger.debug(f"Stream Output - Chunk {chunk_count}: {self._format_long_text(chunk.text, 200)}")
                    yield chunk.text
            
            self.logger.debug(f"Stream completed - Total chunks: {chunk_count}")
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini streaming: {str(e)}")
            yield f"生成回复时出错：{str(e)}"
    
    async def generate_text(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate text response (alias for generate method)."""
        return await self.generate(system_prompt, user_context, file_path)
