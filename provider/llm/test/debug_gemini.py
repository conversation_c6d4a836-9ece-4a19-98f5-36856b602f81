#!/usr/bin/env python3
"""
调试 Gemini API 调用问题的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.llm.test.test_gemini_providers import get_gemini_api_key
from google import genai
from google.genai import types


async def debug_basic_call():
    """调试基本的 API 调用"""
    print("🔍 调试基本 Gemini API 调用")
    print("=" * 50)
    
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    try:
        # 创建客户端
        client = genai.Client(api_key=api_key, http_options={'timeout': 30})
        print("✅ 客户端创建成功")
        
        # 测试同步调用
        print("\n📝 测试同步调用...")
        try:
            response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents="说一句话",
                config=types.GenerateContentConfig(
                    system_instruction="你是一个助手",
                    thinking_config=types.ThinkingConfig(thinking_budget=0)
                )
            )
            print(f"✅ 同步调用成功: {response.text[:100]}...")
        except Exception as sync_error:
            print(f"❌ 同步调用失败: {sync_error}")
            print(f"错误类型: {type(sync_error).__name__}")
            import traceback
            traceback.print_exc()
        
        # 测试异步调用
        print("\n📝 测试异步调用...")
        try:
            response = await client.aio.models.generate_content(
                model="gemini-2.5-flash",
                contents="说一句话",
                config=types.GenerateContentConfig(
                    system_instruction="你是一个助手",
                    thinking_config=types.ThinkingConfig(thinking_budget=0)
                )
            )
            print(f"✅ 异步调用成功: {response.text[:100]}...")
        except Exception as async_error:
            print(f"❌ 异步调用失败: {async_error}")
            print(f"错误类型: {type(async_error).__name__}")
            import traceback
            traceback.print_exc()
        
        # 测试流式调用
        print("\n📝 测试流式调用...")
        try:
            stream = await client.aio.models.generate_content_stream(
                model="gemini-2.5-flash",
                contents="用三句话介绍人工智能",
                config=types.GenerateContentConfig(
                    system_instruction="你是一个助手",
                    thinking_config=types.ThinkingConfig(thinking_budget=0)
                )
            )

            chunks = []
            async for chunk in stream:
                if hasattr(chunk, 'text') and chunk.text:
                    chunks.append(chunk.text)
                    print(f"📦 收到块: {len(chunk.text)} 字符")

            full_response = ''.join(chunks)
            print(f"✅ 流式调用成功，总长度: {len(full_response)} 字符")
            print(f"内容预览: {full_response[:100]}...")

        except Exception as stream_error:
            print(f"❌ 流式调用失败: {stream_error}")
            print(f"错误类型: {type(stream_error).__name__}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def debug_file_upload():
    """调试文件上传"""
    print("\n🔍 调试文件上传")
    print("=" * 50)
    
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    # 检查测试文件
    test_file = Path(__file__).parent / "test_voice.mp3"
    if not test_file.exists():
        print(f"⚠️ 测试文件不存在: {test_file}")
        return False
    
    try:
        client = genai.Client(api_key=api_key, http_options={'timeout': 60})  # 增加超时时间
        
        print(f"📁 上传文件: {test_file}")
        print(f"文件大小: {test_file.stat().st_size} 字节")
        
        # 测试同步上传
        uploaded_file = client.files.upload(file=str(test_file))
        print(f"✅ 文件上传成功: {uploaded_file.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主调试函数"""
    print("🧪 Gemini API 调试工具")
    print("=" * 60)
    
    # 调试基本调用
    basic_success = await debug_basic_call()
    
    # 调试文件上传
    upload_success = await debug_file_upload()
    
    print("\n📋 调试结果总结")
    print("=" * 30)
    print(f"基本 API 调用: {'✅ 成功' if basic_success else '❌ 失败'}")
    print(f"文件上传: {'✅ 成功' if upload_success else '❌ 失败'}")


if __name__ == "__main__":
    asyncio.run(main())
