#!/usr/bin/env python3
"""
测试异步 Gemini 提供者
测试新的 client.aio 异步接口
"""

import asyncio
import sys
import os
import json
import time
import yaml
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.llm.gemini_provider import GeminiProvider
from provider.llm.gemini_search_provider import GeminiSearchProvider


def load_config():
    """从config.yaml加载配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"❌ 无法加载配置文件: {e}")
        return None


def get_gemini_api_key():
    """获取 Gemini API Key"""
    # 首先尝试从配置文件读取
    config = load_config()
    if config and 'api_key' in config and 'google' in config['api_key']:
        return config['api_key']['google']
    
    # 如果配置文件没有，尝试环境变量
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        return api_key
    
    print("❌ 无法获取 Gemini API Key")
    print("   请在 config/config.yaml 中设置 api_key.google")
    print("   或设置环境变量 GEMINI_API_KEY")
    return None


def load_search_agent_config():
    """从search_agent_config.yaml加载SearchAgent配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "search_agent_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"❌ 无法加载SearchAgent配置文件: {e}")
        return None


async def test_gemini_provider_async():
    """测试 GeminiProvider 异步功能"""
    print("🧪 测试 GeminiProvider 异步功能")
    print("=" * 50)
    print()
    
    # 从配置文件获取 API Key
    api_key = get_gemini_api_key()
    if not api_key:
        return False
    
    # 测试配置
    config = {
        'api_key': api_key,
        'model': 'gemini-2.5-flash'
    }
    
    try:
        provider = GeminiProvider(config)
        print("✅ GeminiProvider 初始化成功")
        print()
        
        # 测试 1: 基本文本生成
        print("📝 测试 1: 基本文本生成")
        start_time = time.time()
        
        response = await provider.generate(
            system_prompt="你是一个有用的助手。",
            user_context="用一句话介绍人工智能。"
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 响应时间: {duration:.2f}秒")
        print(f"📄 响应内容: {response[:100]}{'...' if len(response) > 100 else ''}")
        print()
        
        # 测试 2: 流式生成
        print("📝 测试 2: 流式生成")
        start_time = time.time()
        chunks = []
        
        async for chunk in provider.generate_stream(
            system_prompt="你是一个有用的助手。",
            user_context="用三句话介绍机器学习。"
        ):
            chunks.append(chunk)
            print(f"📦 收到流式块: {len(chunk)} 字符")
        
        end_time = time.time()
        duration = end_time - start_time
        full_response = ''.join(chunks)
        
        print(f"✅ 流式响应时间: {duration:.2f}秒")
        print(f"📦 总块数: {len(chunks)}")
        print(f"📄 完整响应: {full_response[:100]}{'...' if len(full_response) > 100 else ''}")
        print()
        
        # 测试 3: 语音文件处理
        print("📝 测试 3: 语音文件处理")
        voice_file = Path(__file__).parent / "test_voice.mp3"
        
        if voice_file.exists():
            start_time = time.time()
            
            response = await provider.generate(
                system_prompt="请仔细听取语音内容，然后回答里面的问题。",
                user_context="请分析这个语音文件的内容。",
                file_path=str(voice_file)
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 语音处理时间: {duration:.2f}秒")
            print(f"📄 语音分析结果: {response[:150]}{'...' if len(response) > 150 else ''}")
        else:
            print("⚠️ 测试语音文件不存在，跳过语音测试")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ GeminiProvider 测试失败: {e}")
        return False


async def test_gemini_search_provider_async():
    """测试 GeminiSearchProvider 异步功能"""
    print("🔍 测试 GeminiSearchProvider 异步功能")
    print("=" * 50)
    print()
    
    # 从配置文件获取 API Key
    api_key = get_gemini_api_key()
    if not api_key:
        return False
    
    # 测试配置
    config = {
        'api_key': api_key,
        'model': 'gemini-2.5-pro',
        'enable_thinking': True
    }
    
    try:
        provider = GeminiSearchProvider(config)
        print("✅ GeminiSearchProvider 初始化成功")
        print()
        
        # 测试 1: 基本搜索生成
        print("📝 测试 1: 基本搜索生成")
        start_time = time.time()
        
        response = await provider.search_and_generate_from_text(
            query="2025年最新的人工智能发展趋势是什么？",
            system_prompt="你是一个AI专家，请提供最新的信息。",
            enable_search=True,
            enable_url_context=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 搜索响应时间: {duration:.2f}秒")
        print(f"📄 搜索结果: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
        print(f"🔧 使用的工具: {response['tools_used']}")
        if response['url_context_metadata']:
            print(f"🌐 URL上下文元数据: 存在")
        print()
        
        # 测试 2: JSON Schema 结构化输出（使用语音文件）
        print("📝 测试 2: JSON Schema 结构化输出（使用语音文件）")
        
        # 从配置文件加载JSON Schema
        search_config = load_search_agent_config()
        if not search_config:
            print("❌ 无法加载SearchAgent配置，跳过此测试")
            print()
            return True
        
        json_schema = search_config.get('json_schema', {})
        main_search_prompt = search_config.get('system_prompts', {}).get('main_search_prompt', '')
        
        voice_file = Path(__file__).parent / "test_voice.mp3"
        
        if voice_file.exists():
            print(f"✅ 使用语音文件: {voice_file}")
            start_time = time.time()
            
            response = await provider.search_and_generate_from_audio(
                audio_file_path=str(voice_file),
                system_prompt=main_search_prompt,
                enable_search=True,
                enable_url_context=True,
                response_schema=json_schema
            )
        else:
            print("⚠️ 测试语音文件不存在，使用文本测试")
            start_time = time.time()
            
            response = await provider.search_and_generate_from_text(
                query="iPhone 15 的价格是多少？",
                system_prompt=main_search_prompt,
                enable_search=True,
                enable_url_context=True,
                response_schema=json_schema
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ JSON Schema响应时间: {duration:.2f}秒")
        print(f"📄 结构化结果: {response['text'][:300]}{'...' if len(response['text']) > 300 else ''}")
        
        # 尝试解析JSON
        try:
            response_text = response['text']
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                parsed_json = json.loads(json_text)
                print("✅ JSON解析成功:")
                for key, value in parsed_json.items():
                    print(f"   {key}: {value}")
            else:
                print("⚠️ 未找到有效的JSON格式")
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
        
        print()
        
        # 测试 3: URL上下文生成
        print("📝 测试 3: URL上下文生成")
        start_time = time.time()
        
        response = await provider.url_context_generate(
            query="请总结这个页面的主要内容：https://news.qq.com/rain/a/20250620A08G1000",
            system_prompt="你是一个内容分析师。",
            enable_thinking=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ URL上下文响应时间: {duration:.2f}秒")
        print(f"📄 URL分析结果: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
        print()
        
        # 测试 4: 音频文件处理（如果存在）
        print("📝 测试 4: 音频文件处理")
        voice_file = Path(__file__).parent / "test_voice.mp3"
        
        if voice_file.exists():
            start_time = time.time()
            
            response = await provider.search_and_generate_from_audio(
                audio_file_path=str(voice_file),
                system_prompt="请仔细听取语音内容，然后根据用户的指令进行搜索并回答问题。",
                enable_search=True,
                enable_url_context=True,
                enable_thinking=True
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 音频搜索响应时间: {duration:.2f}秒")
            print(f"📄 音频搜索结果: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
            print(f"🔧 使用的工具: {response['tools_used']}")
            if response['url_context_metadata']:
                print(f"🌐 URL上下文元数据: 存在")
        else:
            print("⚠️ 测试语音文件不存在，跳过音频测试")
        
        print()
        
        # 测试 5: 流式搜索生成
        print("📝 测试 5: 流式搜索生成")
        start_time = time.time()
        chunks = []
        
        async for chunk in provider.generate_stream(
            system_prompt="你是一个科技分析师。",
            user_context="请介绍一下量子计算的最新进展。"
        ):
            chunks.append(chunk)
            print(f"📦 收到搜索流式块: {len(chunk)} 字符")
        
        end_time = time.time()
        duration = end_time - start_time
        full_response = ''.join(chunks)
        
        print(f"✅ 流式搜索响应时间: {duration:.2f}秒")
        print(f"📦 总块数: {len(chunks)}")
        print(f"📄 完整搜索响应: {full_response[:200]}{'...' if len(full_response) > 200 else ''}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ GeminiSearchProvider 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_performance_comparison():
    """测试性能对比"""
    print("⚡ 性能对比测试")
    print("=" * 50)
    print()
    
    # 从配置文件获取 API Key
    api_key = get_gemini_api_key()
    if not api_key:
        return False
    
    config = {
        'api_key': api_key,
        'model': 'gemini-2.0-flash-001'
    }
    
    try:
        provider = GeminiProvider(config)
        
        # 并发测试
        print("🚀 并发异步调用测试")
        
        tasks = []
        for i in range(3):
            task = provider.generate(
                system_prompt="你是一个助手。",
                user_context=f"请用一句话介绍第{i+1}个有趣的科学事实。"
            )
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        concurrent_duration = end_time - start_time
        
        print(f"✅ 3个并发调用总时间: {concurrent_duration:.2f}秒")
        print(f"📊 平均每个调用: {concurrent_duration/3:.2f}秒")
        for i, result in enumerate(results):
            print(f"   结果{i+1}: {result[:50]}{'...' if len(result) > 50 else ''}")
        
        # 串行测试对比
        print("\n🔄 串行调用测试")
        start_time = time.time()
        
        for i in range(3):
            result = await provider.generate(
                system_prompt="你是一个助手。",
                user_context=f"请用一句话介绍第{i+1}个有趣的科学事实。"
            )
        
        end_time = time.time()
        serial_duration = end_time - start_time
        
        print(f"✅ 3个串行调用总时间: {serial_duration:.2f}秒")
        print(f"📊 平均每个调用: {serial_duration/3:.2f}秒")
        
        # 性能提升计算
        if serial_duration > 0:
            improvement = ((serial_duration - concurrent_duration) / serial_duration) * 100
            print(f"\n🏆 并发性能提升: {improvement:.1f}%")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 Gemini 异步提供者测试套件")
    print("=" * 60)
    print()
    
    # 检查 API Key 可用性
    api_key = get_gemini_api_key()
    if not api_key:
        return
    
    test_results = []
    
    # 测试 GeminiProvider
    result1 = await test_gemini_provider_async()
    test_results.append(("GeminiProvider", result1))
    
    # 测试 GeminiSearchProvider
    result2 = await test_gemini_search_provider_async()
    test_results.append(("GeminiSearchProvider", result2))
    
    # 性能对比测试
    result3 = await test_performance_comparison()
    test_results.append(("性能对比", result3))
    
    # 总结
    print("📋 测试结果总结")
    print("=" * 30)
    print()
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    print()
    print(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！异步 Gemini 提供者工作正常")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    asyncio.run(main())