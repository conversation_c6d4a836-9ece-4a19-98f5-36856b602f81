#!/usr/bin/env python3
"""
测试异步 Gemini 提供者
测试新的 client.aio 异步接口
"""

import asyncio
import sys
import os
import time
import yaml
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.llm.gemini_provider import GeminiProvider
from provider.llm.gemini_search_provider import GeminiSearchProvider


def load_config():
    """从config.yaml加载配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"❌ 无法加载配置文件: {e}")
        return None


def get_gemini_api_key():
    """获取 Gemini API Key"""
    # 首先尝试从配置文件读取
    config = load_config()
    if config and 'api_key' in config and 'google' in config['api_key']:
        return config['api_key']['google']
    
    # 如果配置文件没有，尝试环境变量
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        return api_key
    
    print("❌ 无法获取 Gemini API Key")
    print("   请在 config/config.yaml 中设置 api_key.google")
    print("   或设置环境变量 GEMINI_API_KEY")
    return None


def load_search_agent_config():
    """从search_agent_config.yaml加载SearchAgent配置"""
    try:
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "search_agent_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"❌ 无法加载SearchAgent配置文件: {e}")
        return None


async def test_gemini_provider_async():
    """测试 GeminiProvider 异步功能"""
    print("🧪 测试 GeminiProvider 异步功能")
    print("=" * 50)
    print()
    
    # 从配置文件获取 API Key
    api_key = get_gemini_api_key()
    if not api_key:
        return False
    
    # 测试配置
    config = {
        'api_key': api_key,
        'model': 'gemini-2.5-flash'
    }
    
    try:
        provider = GeminiProvider(config)
        print("✅ GeminiProvider 初始化成功")
        print()
        
        # 测试 1: 基本文本生成
        print("📝 测试 1: 基本文本生成")
        start_time = time.time()
        
        response = await provider.generate(
            system_prompt="你是一个有用的助手。",
            user_context="用一句话介绍人工智能。"
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 响应时间: {duration:.2f}秒")
        print(f"📄 响应内容: {response[:100]}{'...' if len(response) > 100 else ''}")
        print()
        
        # 测试 2: 流式生成
        print("📝 测试 2: 流式生成")
        start_time = time.time()
        chunks = []
        
        async for chunk in provider.generate_stream(
            system_prompt="你是一个有用的助手。",
            user_context="用三句话介绍机器学习。"
        ):
            chunks.append(chunk)
            print(f"📦 收到流式块: {len(chunk)} 字符")
        
        end_time = time.time()
        duration = end_time - start_time
        full_response = ''.join(chunks)
        
        print(f"✅ 流式响应时间: {duration:.2f}秒")
        print(f"📦 总块数: {len(chunks)}")
        print(f"📄 完整响应: {full_response[:100]}{'...' if len(full_response) > 100 else ''}")
        print()
        
        # 测试 3: 语音文件处理
        print("📝 测试 3: 语音文件处理")
        voice_file = Path(__file__).parent / "test_voice.mp3"
        
        if voice_file.exists():
            start_time = time.time()
            
            response = await provider.generate(
                system_prompt="请仔细听取语音内容，然后回答里面的问题。",
                user_context="请分析这个语音文件的内容。",
                file_path=str(voice_file)
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 语音处理时间: {duration:.2f}秒")
            print(f"📄 语音分析结果: {response[:150]}{'...' if len(response) > 150 else ''}")
        else:
            print("⚠️ 测试语音文件不存在，跳过语音测试")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ GeminiProvider 测试失败: {e}")
        return False


async def test_gemini_search_provider_async():
    """测试 GeminiSearchProvider 核心功能"""
    print("🔍 GeminiSearchProvider 核心功能测试")
    print("=" * 50)
    print("测试三大核心能力：文本搜索、语音搜索、URL总结")
    print()
    
    # 从配置文件获取 API Key
    api_key = get_gemini_api_key()
    if not api_key:
        return False
    
    # 测试配置
    config = {
        'api_key': api_key,
        'model': 'gemini-2.5-pro',
        'enable_thinking': True
    }
    
    try:
        provider = GeminiSearchProvider(config)
        print("✅ GeminiSearchProvider 初始化成功")
        print()
        
        # 测试 1: 文本搜索能力
        print("📝 测试 1: 文本搜索能力")
        print("测试 GeminiSearchProvider 的文本搜索和生成能力")
        start_time = time.time()

        response = await provider.generate_with_metadata(
            user_context="2025年最新的人工智能发展趋势是什么？请提供具体的技术进展和应用案例。",
            system_prompt="你是一个专业的AI技术分析师，请提供准确、详细的信息。",
            enable_search=True,
            enable_url_context=True,
            enable_thinking=True
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ 文本搜索成功，响应时间: {duration:.2f}秒")
        print(f"📄 搜索结果长度: {len(response['text'])} 字符")
        print(f"🔧 使用的工具: {response['tools_used']}")
        print(f"📋 结果预览: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
        if response['url_context_metadata']:
            print(f"🌐 URL上下文元数据: 存在")
        print()
        
        # 测试 2: 语音搜索能力
        print("📝 测试 2: 语音搜索能力")
        print("测试 GeminiSearchProvider 的音频输入处理和搜索能力")

        voice_file = Path(__file__).parent / "test_voice.mp3"

        if voice_file.exists():
            print(f"✅ 找到测试语音文件: {voice_file}")
            start_time = time.time()

            response = await provider.generate_with_metadata(
                user_context="请分析这个音频内容，并搜索相关信息进行补充。",
                system_prompt="你是一个专业的音频内容分析师，请仔细听取语音内容，然后根据用户的指令进行搜索并回答问题。",
                file_path=str(voice_file),
                enable_search=True,
                enable_url_context=True,
                enable_thinking=True
            )

            end_time = time.time()
            duration = end_time - start_time

            print(f"✅ 语音搜索成功，响应时间: {duration:.2f}秒")
            print(f"📄 搜索结果长度: {len(response['text'])} 字符")
            print(f"🔧 使用的工具: {response['tools_used']}")
            print(f"📋 结果预览: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
            if response['url_context_metadata']:
                print(f"🌐 URL上下文元数据: 存在")
        else:
            print("⚠️ 测试语音文件不存在，跳过语音搜索测试")
            print("💡 要测试语音搜索能力，请在测试目录放置一个 test_voice.mp3 文件")

        print()
        
        # 测试 3: URL Context 总结能力
        print("📝 测试 3: URL Context 总结能力")
        print("测试 GeminiSearchProvider 的网站内容分析和总结能力")
        start_time = time.time()

        response = await provider.url_context_generate(
            query="请总结这些网站的主要内容和特点：https://www.example.com 和 https://news.ycombinator.com",
            urls=["https://www.example.com", "https://news.ycombinator.com"],
            system_prompt="你是一个专业的网站内容分析专家，请提供详细的分析和总结。",
            enable_thinking=True
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ URL总结成功，响应时间: {duration:.2f}秒")
        print(f"📄 总结结果长度: {len(response['text'])} 字符")
        print(f"🔧 使用的工具: {response['tools_used']}")
        print(f"📋 结果预览: {response['text'][:200]}{'...' if len(response['text']) > 200 else ''}")
        print()
        

        
        print("=" * 50)
        print("🎉 GeminiSearchProvider 核心功能测试完成！")
        print("📊 测试总结:")
        print("  1. ✅ 文本搜索能力 - 搜索并生成基于文本查询的回答")
        print("  2. ✅ 语音搜索能力 - 处理音频输入并搜索相关信息")
        print("  3. ✅ URL总结能力 - 分析和总结指定网站内容")
        print("=" * 50)

        return True

    except Exception as e:
        print(f"❌ GeminiSearchProvider 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🧪 Gemini 异步提供者测试套件")
    print("=" * 60)
    print()
    
    # 检查 API Key 可用性
    api_key = get_gemini_api_key()
    if not api_key:
        return
    
    test_results = []
    
    # 测试 GeminiProvider
    result1 = await test_gemini_provider_async()
    test_results.append(("GeminiProvider", result1))
    
    # 测试 GeminiSearchProvider
    # result2 = await test_gemini_search_provider_async()
    # test_results.append(("GeminiSearchProvider", result2))
    
    # 总结
    print("📋 测试结果总结")
    print("=" * 30)
    print()
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    print()
    print(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！异步 Gemini 提供者工作正常")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    asyncio.run(main())