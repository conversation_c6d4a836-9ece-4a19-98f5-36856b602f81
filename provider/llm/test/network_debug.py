#!/usr/bin/env python3
"""
网络连接诊断脚本
"""

import asyncio
import socket
import ssl
import time
from urllib.parse import urlparse


async def test_dns_resolution():
    """测试 DNS 解析"""
    print("🔍 测试 DNS 解析")
    print("-" * 30)
    
    hosts = [
        "generativelanguage.googleapis.com",
        "googleapis.com",
        "google.com"
    ]
    
    for host in hosts:
        try:
            start_time = time.time()
            result = socket.gethostbyname(host)
            end_time = time.time()
            print(f"✅ {host} -> {result} ({end_time - start_time:.3f}s)")
        except Exception as e:
            print(f"❌ {host} -> 解析失败: {e}")


async def test_tcp_connection():
    """测试 TCP 连接"""
    print("\n🔍 测试 TCP 连接")
    print("-" * 30)
    
    endpoints = [
        ("generativelanguage.googleapis.com", 443),
        ("googleapis.com", 443),
        ("google.com", 443)
    ]
    
    for host, port in endpoints:
        try:
            start_time = time.time()
            
            # 创建连接
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=10.0
            )
            
            end_time = time.time()
            print(f"✅ {host}:{port} 连接成功 ({end_time - start_time:.3f}s)")
            
            # 关闭连接
            writer.close()
            await writer.wait_closed()
            
        except asyncio.TimeoutError:
            print(f"❌ {host}:{port} 连接超时")
        except Exception as e:
            print(f"❌ {host}:{port} 连接失败: {e}")


async def test_ssl_connection():
    """测试 SSL 连接"""
    print("\n🔍 测试 SSL 连接")
    print("-" * 30)
    
    host = "generativelanguage.googleapis.com"
    port = 443
    
    try:
        start_time = time.time()
        
        # 创建 SSL 上下文
        ssl_context = ssl.create_default_context()
        
        # 创建 SSL 连接
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(host, port, ssl=ssl_context),
            timeout=15.0
        )
        
        end_time = time.time()
        print(f"✅ SSL 连接成功 ({end_time - start_time:.3f}s)")
        
        # 获取证书信息
        ssl_object = writer.get_extra_info('ssl_object')
        if ssl_object:
            cert = ssl_object.getpeercert()
            print(f"📜 证书主题: {cert.get('subject', 'Unknown')}")
            print(f"📅 证书有效期: {cert.get('notAfter', 'Unknown')}")
        
        # 关闭连接
        writer.close()
        await writer.wait_closed()
        
    except asyncio.TimeoutError:
        print(f"❌ SSL 连接超时")
    except Exception as e:
        print(f"❌ SSL 连接失败: {e}")


async def test_http_request():
    """测试 HTTP 请求"""
    print("\n🔍 测试 HTTP 请求")
    print("-" * 30)
    
    try:
        import httpx
        
        # 测试简单的 HTTP 请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            start_time = time.time()
            
            response = await client.get("https://www.google.com")
            
            end_time = time.time()
            print(f"✅ HTTP 请求成功 ({end_time - start_time:.3f}s)")
            print(f"📊 状态码: {response.status_code}")
            print(f"📏 响应大小: {len(response.content)} 字节")
            
    except Exception as e:
        print(f"❌ HTTP 请求失败: {e}")


async def check_proxy_settings():
    """检查代理设置"""
    print("\n🔍 检查代理设置")
    print("-" * 30)
    
    import os
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    found_proxy = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"🔧 {var} = {value}")
            found_proxy = True
    
    if not found_proxy:
        print("✅ 未检测到代理设置")
    else:
        print("⚠️ 检测到代理设置，可能影响连接")


async def main():
    """主诊断函数"""
    print("🧪 网络连接诊断工具")
    print("=" * 60)
    
    # 检查代理设置
    await check_proxy_settings()
    
    # DNS 解析测试
    await test_dns_resolution()
    
    # TCP 连接测试
    await test_tcp_connection()
    
    # SSL 连接测试
    await test_ssl_connection()
    
    # HTTP 请求测试
    await test_http_request()
    
    print("\n📋 诊断完成")
    print("=" * 30)
    print("如果所有测试都失败，可能的原因：")
    print("1. 网络防火墙阻止了连接")
    print("2. 公司网络需要代理设置")
    print("3. DNS 服务器配置问题")
    print("4. 本地网络连接问题")


if __name__ == "__main__":
    asyncio.run(main())
