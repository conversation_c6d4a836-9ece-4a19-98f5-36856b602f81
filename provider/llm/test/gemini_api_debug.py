#!/usr/bin/env python3
"""
专门测试 Gemini API 连接的脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from provider.llm.test.test_gemini_providers import get_gemini_api_key
import httpx


async def test_direct_api_call():
    """直接测试 Gemini API 调用"""
    print("🔍 直接测试 Gemini API")
    print("=" * 50)
    
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    # Gemini API 端点
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": api_key
    }
    
    payload = {
        "contents": [
            {
                "parts": [
                    {"text": "说一句话"}
                ]
            }
        ],
        "systemInstruction": {
            "parts": [
                {"text": "你是一个助手"}
            ]
        },
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 100
        }
    }
    
    try:
        # 测试不同的超时设置
        timeout_settings = [10, 30, 60, 120]
        
        for timeout in timeout_settings:
            print(f"\n📝 测试超时设置: {timeout}秒")
            
            try:
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(url, json=payload, headers=headers)
                    
                    print(f"✅ 请求成功！状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        if 'candidates' in result and result['candidates']:
                            text = result['candidates'][0]['content']['parts'][0]['text']
                            print(f"📄 响应内容: {text}")
                            return True
                        else:
                            print(f"⚠️ 响应格式异常: {result}")
                    else:
                        print(f"❌ API 错误: {response.status_code}")
                        print(f"错误内容: {response.text}")
                    
                    break  # 成功就退出循环
                    
            except httpx.ConnectTimeout:
                print(f"❌ 连接超时 ({timeout}秒)")
                continue
            except httpx.ReadTimeout:
                print(f"❌ 读取超时 ({timeout}秒)")
                continue
            except Exception as e:
                print(f"❌ 请求失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_streaming_api():
    """测试流式 API"""
    print("\n🔍 测试流式 API")
    print("=" * 50)
    
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    # 流式 API 端点
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:streamGenerateContent"
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": api_key
    }
    
    payload = {
        "contents": [
            {
                "parts": [
                    {"text": "用三句话介绍人工智能"}
                ]
            }
        ],
        "systemInstruction": {
            "parts": [
                {"text": "你是一个助手"}
            ]
        }
    }
    
    try:
        async with httpx.AsyncClient(timeout=60) as client:
            async with client.stream('POST', url, json=payload, headers=headers) as response:
                print(f"✅ 流式请求开始，状态码: {response.status_code}")
                
                if response.status_code == 200:
                    chunks = []
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            chunks.append(chunk)
                            print(f"📦 收到块: {len(chunk)} 字符")
                    
                    print(f"✅ 流式请求完成，总块数: {len(chunks)}")
                    return True
                else:
                    print(f"❌ 流式请求失败: {response.status_code}")
                    error_text = await response.aread()
                    print(f"错误内容: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 流式测试失败: {e}")
        return False


async def test_file_upload_api():
    """测试文件上传 API"""
    print("\n🔍 测试文件上传 API")
    print("=" * 50)
    
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    # 检查测试文件
    test_file = Path(__file__).parent / "test_voice.mp3"
    if not test_file.exists():
        print(f"⚠️ 测试文件不存在: {test_file}")
        return False
    
    # 文件上传 API 端点
    url = "https://generativelanguage.googleapis.com/upload/v1beta/files"
    
    headers = {
        "x-goog-api-key": api_key
    }
    
    try:
        async with httpx.AsyncClient(timeout=120) as client:  # 文件上传需要更长时间
            with open(test_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'metadata': '{"file": {"display_name": "test_audio"}}'
                }
                
                response = await client.post(url, files=files, data=data, headers=headers)
                
                print(f"📁 文件上传状态码: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    result = response.json()
                    print(f"✅ 文件上传成功: {result.get('name', 'Unknown')}")
                    return True
                else:
                    print(f"❌ 文件上传失败: {response.status_code}")
                    print(f"错误内容: {response.text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 Gemini API 连接诊断")
    print("=" * 60)
    
    # 测试基本 API 调用
    basic_success = await test_direct_api_call()
    
    # 测试流式 API
    stream_success = await test_streaming_api()
    
    # 测试文件上传
    upload_success = await test_file_upload_api()
    
    print("\n📋 测试结果总结")
    print("=" * 30)
    print(f"基本 API 调用: {'✅ 成功' if basic_success else '❌ 失败'}")
    print(f"流式 API 调用: {'✅ 成功' if stream_success else '❌ 失败'}")
    print(f"文件上传 API: {'✅ 成功' if upload_success else '❌ 失败'}")
    
    if not any([basic_success, stream_success, upload_success]):
        print("\n💡 建议:")
        print("1. 检查 API Key 是否有效")
        print("2. 检查 API 配额是否用完")
        print("3. 尝试使用 VPN 或更换网络")
        print("4. 检查防火墙设置")


if __name__ == "__main__":
    asyncio.run(main())
