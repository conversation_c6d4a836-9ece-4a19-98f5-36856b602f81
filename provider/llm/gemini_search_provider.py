from google import genai
from google.genai import types
from google.genai.types import Tool, GenerateContentConfig
from typing import Dict, Any, Optional, List, AsyncGenerator
from .llm_base import LLMBase


class GeminiSearchProvider(LLMBase):
    """Gemini provider with Google Search and URL Context capabilities."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini Search provider with configuration."""
        super().__init__(config)
        
        # Read directly from config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gemini-2.5-pro')
        self.enable_thinking = config.get('enable_thinking', True)
        
        if not self.api_key:
            raise ValueError("Gemini API key not found in configuration")
        
        # Initialize Gemini client with timeout settings
        from google.genai import types
        self.client = genai.Client(
            api_key=self.api_key,
            http_options=types.HttpOptions(timeout=120)
        )

        self.logger.info(f"Gemini Search provider initialized with model: {self.model}, thinking: {self.enable_thinking}")
    
    def _build_tools(self, enable_search: bool = True, enable_url_context: bool = True) -> List[Tool]:
        """Build tools list based on requirements."""
        tools = []
        
        if enable_url_context:
            tools.append(Tool(url_context=types.UrlContext))
            
        if enable_search:
            tools.append(Tool(google_search=types.GoogleSearch))
            
        return tools
    

    

    
    async def url_context_generate(
        self,
        query: str,
        urls: Optional[List[str]] = None,
        system_prompt: Optional[str] = None,
        enable_thinking: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Generate response using URL context only (no search).
        
        Args:
            query: User query/question
            urls: Optional list of URLs to include in the query
            system_prompt: Optional system prompt
            enable_thinking: Whether to enable thinking (overrides instance setting)
            
        Returns:
            Dict containing response text and metadata
        """
        try:
            # Build URL context tool
            url_context_tool = Tool(url_context=types.UrlContext)
            
            # Prepare content (query only)
            contents = query

            # If specific URLs are provided, include them in the query
            if urls:
                url_text = " and ".join(urls)
                contents = contents.replace("YOUR_URL", url_text).replace("YOUR_URL1", urls[0] if len(urls) > 0 else "YOUR_URL1").replace("YOUR_URL2", urls[1] if len(urls) > 1 else "YOUR_URL2")

            # Use parameter value if provided, otherwise use instance setting
            thinking_enabled = enable_thinking if enable_thinking is not None else self.enable_thinking

            # Create thinking config
            if thinking_enabled:
                thinking_config = types.ThinkingConfig(thinking_budget=-1)
            else:
                thinking_config = types.ThinkingConfig(thinking_budget=0)

            # Create generation config
            generation_config = GenerateContentConfig(
                tools=[url_context_tool],
                response_modalities=["TEXT"],
                thinking_config=thinking_config,
                system_instruction=system_prompt  # Pass system prompt correctly
            )

            # Generate response (using async client)
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            # Extract response text
            response_text = ""
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
            
            # Extract metadata
            url_context_metadata = None
            if hasattr(response.candidates[0], 'url_context_metadata'):
                url_context_metadata = response.candidates[0].url_context_metadata
            
            return {
                "text": response_text,
                "url_context_metadata": url_context_metadata,
                "tools_used": ["UrlContext"]
            }
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini URL context generation: {str(e)}")
            return {
                "text": f"URL内容生成回复时出错：{str(e)}",
                "url_context_metadata": None,
                "tools_used": []
            }

    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[callable] = None,
        enable_search: bool = True,
        enable_url_context: bool = True,
        enable_thinking: Optional[bool] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        Generate streaming response with search capabilities.
        Supports both text and audio input, uses Gemini's native streaming API.

        Args:
            system_prompt: System instruction for the model
            user_context: User query/question text
            file_path: Optional path to audio file (WAV or MP3)
            callback: Optional async callback function for each chunk
            enable_search: Whether to enable Google Search
            enable_url_context: Whether to enable URL Context reading
            enable_thinking: Whether to enable thinking (overrides instance setting)
            response_schema: Optional JSON schema to enforce structured output
        """
        try:
            # DEBUG: Log streaming input
            self.logger.debug(f"Stream Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Stream Input - User context: {self._format_long_text(user_context)}")
            self.logger.debug(f"Stream Input - File path: {file_path if file_path else 'None'}")

            # Build tools for search and URL context
            tools = self._build_tools(enable_search, enable_url_context)

            # Handle audio file if provided
            if file_path:
                try:
                    import os

                    # Get file size and format for logging
                    file_size = os.path.getsize(file_path)
                    file_extension = os.path.splitext(file_path)[1].lower()

                    self.logger.debug(f"Audio file upload - Path: {file_path}, Size: {file_size} bytes, Format: {file_extension}")

                    uploaded_file = await self.client.aio.files.upload(file=file_path)

                    self.logger.info(f"Audio file uploaded for Gemini analysis: {file_path} (format: {file_extension})")
                    self.logger.debug(f"Audio file upload successful - File ID: {uploaded_file.name if hasattr(uploaded_file, 'name') else 'unknown'}")

                    # Prepare content with uploaded file
                    contents = [uploaded_file]

                except Exception as e:
                    self.logger.error(f"Failed to upload audio file to Gemini: {str(e)}")
                    error_message = f"音频文件上传失败：{str(e)}"

                    if callback:
                        try:
                            await callback(error_message)
                        except Exception:
                            pass

                    yield error_message
                    return
            else:
                # Prepare content with text only
                contents = user_context

            # Use parameter value if provided, otherwise use instance setting
            thinking_enabled = enable_thinking if enable_thinking is not None else self.enable_thinking

            # Create thinking config
            if thinking_enabled:
                thinking_config = types.ThinkingConfig(thinking_budget=-1)
            else:
                thinking_config = types.ThinkingConfig(thinking_budget=0)

            # Create generation config for streaming
            generation_config = GenerateContentConfig(
                tools=tools,
                response_modalities=["TEXT"],
                thinking_config=thinking_config,
                system_instruction=system_prompt  # Pass system prompt correctly
            )

            # Add JSON schema to generation config if provided
            if response_schema:
                generation_config.response_mime_type = "application/json"
                generation_config.response_schema = response_schema

            # DEBUG: Log generation config
            self.logger.debug(f"Stream Config - Model: {self.model}, Thinking enabled: {thinking_enabled}")
            self.logger.debug(f"Stream Config - Tools enabled: search={enable_search}, url_context={enable_url_context}")

            # Generate streaming response using Gemini's native streaming API
            stream = self.client.aio.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=generation_config
            )

            # Process streaming chunks
            async for chunk in stream:
                if chunk.candidates and len(chunk.candidates) > 0:
                    candidate = chunk.candidates[0]
                    if candidate.content and candidate.content.parts:
                        for part in candidate.content.parts:
                            if hasattr(part, 'text') and part.text:
                                chunk_text = part.text
                                self.logger.debug(f"Stream Output - Yielding chunk: {len(chunk_text)} chars")

                                # Call callback if provided
                                if callback:
                                    try:
                                        await callback(chunk_text)
                                    except Exception as cb_error:
                                        self.logger.warning(f"Callback error: {cb_error}")

                                yield chunk_text

        except Exception as e:
            self.logger.error(f"Error in Gemini streaming generation: {str(e)}")
            error_message = f"流式生成回复时出错：{str(e)}"

            # Call callback for error if provided
            if callback:
                try:
                    await callback(error_message)
                except Exception:
                    pass

            yield error_message

    async def generate(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate non-streaming response with search capabilities."""
        # Collect all chunks from the streaming response
        response_chunks = []
        async for chunk in self.generate_stream(
            system_prompt=system_prompt,
            user_context=user_context,
            file_path=file_path,
            enable_search=True,
            enable_url_context=True
        ):
            response_chunks.append(chunk)

        return ''.join(response_chunks)

    async def generate_with_metadata(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        enable_search: bool = True,
        enable_url_context: bool = True,
        enable_thinking: Optional[bool] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate response with metadata (for compatibility with SearchAgent).
        Returns dict with 'text' key and other metadata.
        """
        # Collect all chunks from the streaming response
        response_chunks = []
        async for chunk in self.generate_stream(
            system_prompt=system_prompt,
            user_context=user_context,
            file_path=file_path,
            enable_search=enable_search,
            enable_url_context=enable_url_context,
            enable_thinking=enable_thinking,
            response_schema=response_schema
        ):
            response_chunks.append(chunk)

        response_text = ''.join(response_chunks)

        # Return in the expected format
        return {
            "text": response_text,
            "url_context_metadata": None,  # TODO: Extract from streaming response if needed
            "tools_used": self._get_tools_used(enable_search, enable_url_context)
        }

    def _get_tools_used(self, enable_search: bool, enable_url_context: bool) -> List[str]:
        """Get list of tool names based on enabled features."""
        tools = []
        if enable_url_context:
            tools.append("UrlContext")
        if enable_search:
            tools.append("GoogleSearch")
        return tools


