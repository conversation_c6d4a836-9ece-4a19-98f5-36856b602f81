from google import genai
from google.genai import types
from google.genai.types import Tool, GenerateContentConfig
from typing import Dict, Any, Optional, List, AsyncGenerator
from .llm_base import LLMBase


class GeminiSearchProvider(LLMBase):
    """Gemini provider with Google Search and URL Context capabilities."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini Search provider with configuration."""
        super().__init__(config)
        
        # Read directly from config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gemini-2.5-pro')
        self.enable_thinking = config.get('enable_thinking', True)
        
        if not self.api_key:
            raise ValueError("Gemini API key not found in configuration")
        
        # Initialize Gemini client with timeout settings
        import httpx
        timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=10.0)
        self.client = genai.Client(api_key=self.api_key, http_options={'timeout': timeout})

        self.logger.info(f"Gemini Search provider initialized with model: {self.model}, thinking: {self.enable_thinking}")
    
    def _build_tools(self, enable_search: bool = True, enable_url_context: bool = True) -> List[Tool]:
        """Build tools list based on requirements."""
        tools = []
        
        if enable_url_context:
            tools.append(Tool(url_context=types.UrlContext))
            
        if enable_search:
            tools.append(Tool(google_search=types.GoogleSearch))
            
        return tools
    
    async def search_and_generate_from_audio(
        self,
        audio_file_path: str,
        system_prompt: Optional[str] = None,
        enable_search: bool = True,
        enable_url_context: bool = True,
        enable_thinking: Optional[bool] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate response with search and URL context capabilities from audio file.
        
        Args:
            audio_file_path: Path to WAV or MP3 audio file
            system_prompt: Optional system prompt
            enable_search: Whether to enable Google Search
            enable_url_context: Whether to enable URL Context reading
            enable_thinking: Whether to enable thinking (overrides instance setting)
            response_schema: Optional JSON schema to enforce structured output
            
        Returns:
            Dict containing response text and metadata
        """
        try:
            tools = self._build_tools(enable_search, enable_url_context)
            
            # Upload audio file to Gemini
            try:
                import os
                
                # Get file size and format for logging
                file_size = os.path.getsize(audio_file_path)
                file_extension = os.path.splitext(audio_file_path)[1].lower()
                
                self.logger.debug(f"Audio file upload - Path: {audio_file_path}, Size: {file_size} bytes, Format: {file_extension}")
                
                uploaded_file = await self.client.aio.files.upload(file=audio_file_path)
                
                self.logger.info(f"Audio file uploaded for Gemini analysis: {audio_file_path} (format: {file_extension})")
                self.logger.debug(f"Audio file upload successful - File ID: {uploaded_file.name if hasattr(uploaded_file, 'name') else 'unknown'}")
                
            except Exception as e:
                self.logger.error(f"Failed to upload audio file to Gemini: {str(e)}")
                return {
                    "text": f"音频文件上传失败：{str(e)}",
                    "url_context_metadata": None,
                    "tools_used": []
                }
            
            # Prepare content with uploaded file and system prompt
            if system_prompt:
                contents = [system_prompt, uploaded_file]
            else:
                contents = [uploaded_file]
            
            # Add JSON schema instruction if provided
            if response_schema:
                import json
                schema_str = json.dumps(response_schema, ensure_ascii=False, indent=2)
                schema_instruction = f"\n\n请严格按照以下JSON Schema格式返回结果：\n{schema_str}\n\n只返回有效的JSON格式，不要包含其他文字。"
                if system_prompt:
                    contents[0] = system_prompt + schema_instruction
                else:
                    contents.insert(0, schema_instruction)

            # DEBUG: Log input parameters
            self.logger.debug(f"Input - Audio file: {audio_file_path}")
            self.logger.debug(f"Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Input - Tools enabled: search={enable_search}, url_context={enable_url_context}")

            # Use parameter value if provided, otherwise use instance setting
            thinking_enabled = enable_thinking if enable_thinking is not None else self.enable_thinking
            
            # Create thinking config
            if thinking_enabled:
                thinking_config = types.ThinkingConfig(thinking_budget=-1)
            else:
                thinking_config = types.ThinkingConfig(thinking_budget=0)

            # Create generation config
            generation_config = GenerateContentConfig(
                tools=tools,
                response_modalities=["TEXT"],
                thinking_config=thinking_config
            )

            # DEBUG: Log generation config
            self.logger.debug(f"Config - Model: {self.model}, Thinking enabled: {thinking_enabled}")
            
            # Generate response (using async client)
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            # Extract response text
            response_text = ""
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
            
            # DEBUG: Log output
            self.logger.debug(f"Output - Response text: {self._format_long_text(response_text)}")
            
            # Extract metadata
            url_context_metadata = None
            if hasattr(response.candidates[0], 'url_context_metadata'):
                url_context_metadata = response.candidates[0].url_context_metadata
                self.logger.debug(f"Output - URL context metadata available: {bool(url_context_metadata)}")
            
            return {
                "text": response_text,
                "url_context_metadata": url_context_metadata,
                "tools_used": [tool.__class__.__name__ for tool in tools]
            }
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini audio search generation: {str(e)}")
            return {
                "text": f"音频搜索生成回复时出错：{str(e)}",
                "url_context_metadata": None,
                "tools_used": []
            }
    
    async def search_and_generate_from_text(
        self,
        query: str,
        system_prompt: Optional[str] = None,
        enable_search: bool = True,
        enable_url_context: bool = True,
        enable_thinking: Optional[bool] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate response with search and URL context capabilities from text query.
        
        Args:
            query: User query/question text
            system_prompt: Optional system prompt
            enable_search: Whether to enable Google Search
            enable_url_context: Whether to enable URL Context reading
            enable_thinking: Whether to enable thinking (overrides instance setting)
            response_schema: Optional JSON schema to enforce structured output
            
        Returns:
            Dict containing response text and metadata
        """
        try:
            tools = self._build_tools(enable_search, enable_url_context)
            
            # Prepare content with system prompt if provided
            if system_prompt:
                contents = f"{system_prompt}\n\n{query}"
            else:
                contents = query
            
            # Add JSON schema instruction if provided
            if response_schema:
                import json
                schema_str = json.dumps(response_schema, ensure_ascii=False, indent=2)
                contents += f"\n\n请严格按照以下JSON Schema格式返回结果：\n{schema_str}\n\n只返回有效的JSON格式，不要包含其他文字。"

            # DEBUG: Log input parameters
            self.logger.debug(f"Input - Query: {self._format_long_text(query)}")
            self.logger.debug(f"Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Input - Tools enabled: search={enable_search}, url_context={enable_url_context}")
            self.logger.debug(f"Input - Final contents: {self._format_long_text(contents)}")

            # Use parameter value if provided, otherwise use instance setting
            thinking_enabled = enable_thinking if enable_thinking is not None else self.enable_thinking
            
            # Create thinking config
            if thinking_enabled:
                thinking_config = types.ThinkingConfig(thinking_budget=-1)
            else:
                thinking_config = types.ThinkingConfig(thinking_budget=0)

            # Create generation config
            generation_config = GenerateContentConfig(
                tools=tools,
                response_modalities=["TEXT"],
                thinking_config=thinking_config
            )

            # DEBUG: Log generation config
            self.logger.debug(f"Config - Model: {self.model}, Thinking enabled: {thinking_enabled}")
            
            # Generate response (using async client)
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            # Extract response text
            response_text = ""
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
            
            # DEBUG: Log output
            self.logger.debug(f"Output - Response text: {self._format_long_text(response_text)}")
            
            # Extract metadata
            url_context_metadata = None
            if hasattr(response.candidates[0], 'url_context_metadata'):
                url_context_metadata = response.candidates[0].url_context_metadata
                self.logger.debug(f"Output - URL context metadata available: {bool(url_context_metadata)}")
            
            return {
                "text": response_text,
                "url_context_metadata": url_context_metadata,
                "tools_used": [tool.__class__.__name__ for tool in tools]
            }
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini search generation: {str(e)}")
            return {
                "text": f"搜索生成回复时出错：{str(e)}",
                "url_context_metadata": None,
                "tools_used": []
            }
    
    async def url_context_generate(
        self,
        query: str,
        urls: Optional[List[str]] = None,
        system_prompt: Optional[str] = None,
        enable_thinking: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Generate response using URL context only (no search).
        
        Args:
            query: User query/question
            urls: Optional list of URLs to include in the query
            system_prompt: Optional system prompt
            enable_thinking: Whether to enable thinking (overrides instance setting)
            
        Returns:
            Dict containing response text and metadata
        """
        try:
            # Build URL context tool
            url_context_tool = Tool(url_context=types.UrlContext)
            
            # Prepare content
            if system_prompt:
                contents = f"{system_prompt}\n\n{query}"
            else:
                contents = query
            
            # If specific URLs are provided, include them in the query
            if urls:
                url_text = " and ".join(urls)
                contents = contents.replace("YOUR_URL", url_text).replace("YOUR_URL1", urls[0] if len(urls) > 0 else "YOUR_URL1").replace("YOUR_URL2", urls[1] if len(urls) > 1 else "YOUR_URL2")

            # Use parameter value if provided, otherwise use instance setting
            thinking_enabled = enable_thinking if enable_thinking is not None else self.enable_thinking
            
            # Create thinking config
            if thinking_enabled:
                thinking_config = types.ThinkingConfig(thinking_budget=-1)
            else:
                thinking_config = types.ThinkingConfig(thinking_budget=0)

            # Create generation config
            generation_config = GenerateContentConfig(
                tools=[url_context_tool],
                response_modalities=["TEXT"],
                thinking_config=thinking_config
            )

            # Generate response (using async client)
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            # Extract response text
            response_text = ""
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
            
            # Extract metadata
            url_context_metadata = None
            if hasattr(response.candidates[0], 'url_context_metadata'):
                url_context_metadata = response.candidates[0].url_context_metadata
            
            return {
                "text": response_text,
                "url_context_metadata": url_context_metadata,
                "tools_used": ["UrlContext"]
            }
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini URL context generation: {str(e)}")
            return {
                "text": f"URL内容生成回复时出错：{str(e)}",
                "url_context_metadata": None,
                "tools_used": []
            }

    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[callable] = None
    ) -> AsyncGenerator[str, None]:
        """
        Generate streaming response - simplified implementation.
        For GeminiSearchProvider, we use non-streaming search then yield the result.
        """
        try:
            # DEBUG: Log streaming input
            self.logger.debug(f"Stream Input - System prompt: {self._format_long_text(system_prompt)}")
            self.logger.debug(f"Stream Input - User context: {self._format_long_text(user_context)}")
            self.logger.debug(f"Stream Input - File path: {file_path if file_path else 'None'}")
            
            # Use search_and_generate_from_text for complete response
            result = await self.search_and_generate_from_text(
                query=user_context,
                system_prompt=system_prompt,
                enable_search=True,
                enable_url_context=True
            )
            
            # Yield the complete response as a single chunk
            response_text = result.get("text", "")
            self.logger.debug(f"Stream Output - Yielding response chunk: {self._format_long_text(response_text)}")
            yield response_text
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini search streaming: {str(e)}")
            yield f"搜索生成回复时出错：{str(e)}"

    async def generate(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate non-streaming response with search capabilities."""
        result = await self.search_and_generate_from_text(
            query=user_context,
            system_prompt=system_prompt,
            enable_search=True,
            enable_url_context=True
        )
        return result.get("text", "")
    
    async def generate_text(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """Generate text response (alias for generate method)."""
        return await self.generate(system_prompt, user_context, file_path)
    
    # Backward compatibility method
    async def search_and_generate(
        self,
        query: str,
        system_prompt: Optional[str] = None,
        enable_search: bool = True,
        enable_url_context: bool = True,
        enable_thinking: Optional[bool] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Backward compatibility method - routes to search_and_generate_from_text.
        
        Args:
            query: User query/question text
            system_prompt: Optional system prompt
            enable_search: Whether to enable Google Search
            enable_url_context: Whether to enable URL Context reading
            enable_thinking: Whether to enable thinking (overrides instance setting)
            response_schema: Optional JSON schema to enforce structured output
            
        Returns:
            Dict containing response text and metadata
        """
        return await self.search_and_generate_from_text(
            query=query,
            system_prompt=system_prompt,
            enable_search=enable_search,
            enable_url_context=enable_url_context,
            enable_thinking=enable_thinking,
            response_schema=response_schema
        )