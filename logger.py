import logging
import sys
from pathlib import Path
from typing import Optional

class ColoredFormatter(logging.Formatter):
    """Custom formatter that adds colors to different parts of log messages"""
    
    # ANSI color codes
    COLORS = {
        'RESET': '\033[0m',
        'TIMESTAMP': '\033[90m',      # <PERSON> (<PERSON>)
        'LOGGER': '\033[96m',         # <PERSON>an
        'DEBUG': '\033[36m',          # Cyan
        'INFO': '\033[92m',           # Green
        'WARNING': '\033[33m',        # Yellow
        'ERROR': '\033[91m',          # Light Red
        'CRITICAL': '\033[95m',       # Magenta
        'MESSAGE': '\033[97m',        # White
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.RESET = self.COLORS['RESET']
    
    def format(self, record):
        # Only add colors if output is to a terminal
        if not sys.stdout.isatty():
            return super().format(record)
            
        # Store the original format
        original_format = self._style._fmt
        
        # Color the timestamp
        timestamp = f"{self.COLORS['TIMESTAMP']}%(asctime)s{self.RESET}"
        
        # Color the thread name
        thread_name = f"{self.COLORS['TIMESTAMP']}[%(threadName)s]{self.RESET}"
        
        # Color the logger name
        logger_name = f"{self.COLORS['LOGGER']}%(name)s{self.RESET}"
        
        # Get the appropriate color for the log level
        level_color = self.COLORS.get(record.levelname, self.COLORS['MESSAGE'])
        
        # Color the log level
        level_name = f"{level_color}%(levelname)s{self.RESET}"
        
        # Color the message
        message = f"{self.COLORS['MESSAGE']}%(message)s{self.RESET}"
        
        # Reconstruct the format with colors
        self._style._fmt = f"{timestamp} - {thread_name} - {logger_name} - {level_name} - {message}"
        
        try:
            # Call the parent's format method
            result = super().format(record)
            return result
        finally:
            # Always restore the original format
            self._style._fmt = original_format

class WebSocketsFilter(logging.Filter):
    """Custom filter to hide BINARY messages from websockets.server while keeping TEXT, PING, PONG, CLOSE"""
    
    def filter(self, record):
        # Only apply filtering to websockets.server logger
        if record.name == 'websockets.server':
            message = record.getMessage()
            # Hide BINARY messages but keep TEXT, PING, PONG, CLOSE messages
            if '< BINARY' in message or '> BINARY' in message:
                return False
        return True

class AppLogger:
    """Centralized logging system for the application"""
    
    _instance: Optional['AppLogger'] = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not AppLogger._initialized:
            self._setup_logging()
            AppLogger._initialized = True
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Create log directory
        log_dir = Path("log")
        log_dir.mkdir(exist_ok=True)
        
        # Create formatters
        file_formatter = logging.Formatter('%(asctime)s - [%(threadName)s] - %(name)s - %(levelname)s - %(message)s')
        console_formatter = ColoredFormatter('%(asctime)s - [%(threadName)s] - %(name)s - %(levelname)s - %(message)s')
        
        # Create handlers
        file_handler = logging.FileHandler(log_dir / "server.log", encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(console_formatter)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers to avoid duplicates
        root_logger.handlers.clear()
        
        # Add our handlers
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        # Create and apply websockets filter
        websockets_filter = WebSocketsFilter()
        
        # Apply filter to both handlers for websockets messages
        file_handler.addFilter(websockets_filter)
        console_handler.addFilter(websockets_filter)
        
        # Set websockets logger to DEBUG to see TEXT, PING, PONG, CLOSE messages
        logging.getLogger('websockets.server').setLevel(logging.DEBUG)
        logging.getLogger('openai._base_client').setLevel(logging.WARNING)
        logging.getLogger('pydub.converter').setLevel(logging.WARNING)
        logging.getLogger('httpcore.http11').setLevel(logging.INFO)
        logging.getLogger('httpcore.connection').setLevel(logging.INFO)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger instance for the given name"""
        return logging.getLogger(name)

# Global instance
_app_logger = AppLogger()

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name"""
    return _app_logger.get_logger(name)
