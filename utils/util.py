import socket
import json
from typing import Optional, Dict, Any


def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Connect to Google's DNS servers
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        return "127.0.0.1"


def parse_llm_json_response(response_text: str, logger=None) -> Optional[Dict[str, Any]]:
    """
    解析 LLM 返回的 JSON 响应，处理 LLM 可能添加的额外文本

    Args:
        response_text: LLM 返回的原始文本
        logger: 可选的日志记录器

    Returns:
        解析成功返回字典，失败返回 None
    """
    try:
        response_text = response_text.strip()

        # 尝试直接解析整个响应
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass

        # 尝试提取 JSON 部分（处理 LLM 添加额外文本的情况）
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1

        if json_start >= 0 and json_end > json_start:
            json_text = response_text[json_start:json_end]
            try:
                parsed_result = json.loads(json_text)
                if logger:
                    logger.debug(f"Successfully extracted JSON from LLM response")
                return parsed_result
            except json.JSONDecodeError:
                pass

        # 尝试查找数组格式的 JSON
        json_start = response_text.find('[')
        json_end = response_text.rfind(']') + 1

        if json_start >= 0 and json_end > json_start:
            json_text = response_text[json_start:json_end]
            try:
                parsed_result = json.loads(json_text)
                if logger:
                    logger.debug(f"Successfully extracted JSON array from LLM response")
                return parsed_result
            except json.JSONDecodeError:
                pass

        if logger:
            logger.error(f"Failed to extract valid JSON from LLM response: {response_text[:200]}...")
        return None

    except Exception as e:
        if logger:
            logger.error(f"Error parsing LLM JSON response: {e}")
        return None