# WebSocket Server Configuration

# Service provider settings (echo, gemini_live)
service:
  type: "agent_event"  # Options: "echo", "agent_event"

# Server settings
server:
  host: "0.0.0.0"
  port: 8001
  path: "/xiaozhi/v1"

api_key:
  openai: "your openai key here"
  google: "your gemini key here"
  doubao_tts:
    app_id: <doubao app_id>
    access_token: "your doubao access token here"

# LLM Provider Settings
llm:
  # Agent-specific LLM configurations
  agents:
    # FastReplyAgent - 快速回复，需要多模态支持
    fast_reply:
      provider: "OpenAIAudioProvider"
      vendor: "openai"
      model: "gpt-4o-audio-preview"
    
    # SearchAgent - 深度分析和搜索，使用Gemini 2.5 Pro Thinking
    search:
      provider: "GeminiSearchProvider"
      vendor: "google"
      model: "gemini-2.5-pro"
      max_retry_attempts: 3  # 最大重试次数
    
    # VadAgent - 语音活动检测，不需要LLM
    vad:
      provider: null  # No LLM needed

  echo:
    provider: "GeminiProvider"
    vendor: "google"
    model: "gemini-2.5-flash"
  
# Text-to-Speech provider configuration
tts:
  provider: "doubao"
  ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
  speaker: "zh_female_shuangkuaisisi_moon_bigtts"
  audio_format: "mp3"
  sample_rate: 24000

# 与客户端通信的WebSocket音频参数
websocket_audio:
  format: "opus"
  sample_rate: 16000
  channels: 1
  frame_duration: 60
  output_dir: "log/audio"  # Directory to store audio files

# 与客户端通信的WebSocket是否有人说话、是否说完了的检测（VAD）参数
websocket_vad:
  threshold: 0.5  # Silero VAD模型的语音检测阈值，范围0-1，越小越敏感
  silence_duration_ms: 500  # 从 Websocket 接收语音流，连续多少毫秒没有语音，就认为用户说的一句话结束了，交给后续处理
  max_speech_duration_ms: 15000  # 连续接收语音流超过多少毫秒没有截断，强行截断为一个wav，以防止背景声音杂乱时一直停不下来
