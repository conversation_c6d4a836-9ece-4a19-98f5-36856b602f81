import asyncio
import socket
import sys
import uuid
import time
from typing import Dict, Any, Optional, List
from websockets.protocol import State

import websockets
from provider import BaseService, service_factory
from logger import get_logger
from .protocol import protocol_handler, MessageType, ListenState, TTSState
from quality import quality_monitor
from utils.util import get_local_ip
from provider.audio_frontend.audio_file_utils import audio_to_data

logger = get_logger(__name__)


class WebSocketServer:
    def __init__(self, config):
        self.config = config
        self.server = None
        self.shutdown_event = asyncio.Event()

    async def handle_connection(self, websocket, path=None):
        """Handle new WebSocket connection"""
        handler = ConnectionHandler(self.config, websocket)
        await handler.handle_connection()

    async def start_server(self):
        server_config = self.config.get("server", {})
        host = server_config.get("host", "0.0.0.0")
        port = server_config.get("port", 8000)
        
        # Check if port is available
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.bind((host, port))
            sock.close()
        except OSError as e:
            logger.critical(f"Port {host}:{port} is already in use")
            sys.exit(1)
        
        # For debugging, show local IP if binding to all interfaces
        if host == "0.0.0.0":
            display_host = get_local_ip()
        else:
            display_host = host
        
        logger.info(f"Starting WebSocket server on ws://{display_host}:{port}/xiaozhi/v1/")
        
        try:
            self.server = await websockets.serve(
                self.handle_connection,
                host,
                port,
                reuse_port=False
            )
            logger.info(f"Successfully bound to {host}:{port}")
            await self.shutdown_event.wait()
        except OSError as e:
            logger.critical(f"Failed to start WebSocket server: {str(e)}")
            sys.exit(1)

    async def shutdown(self):
        logger.info("[SHUTDOWN] Shutting down WebSocket server...")
        
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("[SHUTDOWN] WebSocket server closed")
        
        self.shutdown_event.set()
        logger.info("[SHUTDOWN] Server shutdown complete")

class ConnectionHandler:
    def __init__(self, config, websocket):
        self.config = config
        self.websocket = websocket
        self.session_id = None
        self.handshake_complete = False
        self.service: Optional[BaseService] = None
        self.service_type = config.get("service", {}).get("type")
        if not self.service_type:
            raise ValueError("Service type must be specified in config")
        self.logger = get_logger(self.__class__.__name__)
        self.protocol = protocol_handler
        self.tts_started = False  # 跟踪是否已发送TTS START

    async def _ensure_tts_start(self) -> None:
        """确保发送TTS START消息，让客户端进入等待状态"""
        # Check connection state before sending
        if self.websocket.state in (State.CLOSING, State.CLOSED):
            self.logger.debug("Skipping TTS start - connection closed")
            raise Exception("Connection closed")
            
        if not self.tts_started:
            tts_start = self.protocol.create_tts_message(self.session_id, TTSState.START)
            self.logger.info(f"发送 TTS start 消息: {tts_start}")
            await self.websocket.send(self.protocol.serialize_message(tts_start))
            self.tts_started = True

    async def _send_audio_from_file(self, audio_file_path: str, echo_text: str = "（回声）") -> None:
        """从音频文件读取音频并发送，支持WAV和MP3格式"""
        try:
            # 确保发送TTS START消息，让客户端进入等待状态
            await self._ensure_tts_start()
            
            # 给客户端发送的语音要转成 opus 格式
            opus_packets, duration = audio_to_data(audio_file_path, is_opus=True)
            if not opus_packets:
                self.logger.error(f"Failed to convert audio file to Opus packets: {audio_file_path}")
                return
            
            # 1. 发送TTS sentence_start消息（带文本）
            tts_sentence_start = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_START, echo_text)
            self.logger.debug(f"Sending TTS sentence_start: {tts_sentence_start}")
            await self.websocket.send(self.protocol.serialize_message(tts_sentence_start))
            
            # 2. 发送Opus音频数据流
            self.logger.info(f"Sending {len(opus_packets)} Opus packets from audio file to client (duration: {duration:.2f}s)")
            await self._send_audio_with_timing(opus_packets)
            
            # 3. 发送TTS sentence_end消息（带文本）
            tts_sentence_end = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_END, echo_text)
            self.logger.debug(f"Sending TTS sentence_end: {tts_sentence_end}")
            await self.websocket.send(self.protocol.serialize_message(tts_sentence_end))
            
            # 4. 发送TTS stop消息，并重置tts_started标志
            tts_stop = self.protocol.create_tts_message(self.session_id, TTSState.STOP)
            self.logger.debug(f"Sending TTS stop: {tts_stop}")
            await self.websocket.send(self.protocol.serialize_message(tts_stop))
            self.tts_started = False  # 重置标志，允许下次重新发送TTS START
            
            self.logger.info(f"Audio from file completed - sent {len(opus_packets)} packets from audio file")
            
        except Exception as e:
            self.logger.error(f"Failed to send audio from file: {e}")

    async def _send_stt_message(self, text: str) -> None:
        """发送STT消息 - 用于语音识别结果"""
        # Check connection state before sending
        if self.websocket.state in (State.CLOSING, State.CLOSED):
            self.logger.debug(f"Skipping STT send - connection closed: {text[:50]}...")
            raise Exception("Connection closed")
        
        # Create STT message
        stt_msg = self.protocol.create_stt_message(self.session_id, text)
        self.logger.debug(f"Sending STT message: {stt_msg}")
        await self.websocket.send(self.protocol.serialize_message(stt_msg))
        
        # After STT, automatically start TTS sequence
        await self._ensure_tts_start()
    
    async def _send_text_tts_sequence(self, text: str) -> None:
        """按照通信协议，向客户端发送TTS文本消息（服务端要发送给客户端的文本和语音）"""
        
        # Check connection state before sending
        if self.websocket.state in (State.CLOSING, State.CLOSED):
            self.logger.debug(f"Skipping TTS text send - connection closed: {text[:50]}...")
            raise Exception("Connection closed")

        # 确保发送TTS START消息
        await self._ensure_tts_start()

        # Send TTS sentence messages with the text
        tts_sentence_start = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_START, text)
        self.logger.debug(f"Sending TTS sentence_start: {tts_sentence_start}")
        await self.websocket.send(self.protocol.serialize_message(tts_sentence_start))

        tts_sentence_end = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_END, text)
        self.logger.debug(f"Sending TTS sentence_end: {tts_sentence_end}")
        await self.websocket.send(self.protocol.serialize_message(tts_sentence_end))

        # Send TTS stop message to complete the sequence
        tts_stop = self.protocol.create_tts_message(self.session_id, TTSState.STOP)
        self.logger.debug(f"Sending TTS stop: {tts_stop}")
        await self.websocket.send(self.protocol.serialize_message(tts_stop))
        self.tts_started = False  # 重置标志

    async def _send_audio_data(self, audio_data: bytes, text: str = None) -> None:
        """Send raw audio data to client with proper TTS protocol"""
        try:
            # Record first character received if this is the first response
            if self.session_id:
                quality_monitor.record_first_char_received(self.session_id)
            
            # 确保发送TTS START消息
            await self._ensure_tts_start()
            
            # Send TTS sentence start
            tts_start = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_START, text)
            self.logger.debug(f"Sending TTS sentence_start: {tts_start}")
            await self.websocket.send(self.protocol.serialize_message(tts_start))
            
            # Send audio data
            await self.websocket.send(audio_data)
            
            # Send TTS sentence end
            tts_stop = self.protocol.create_tts_message(self.session_id, TTSState.SENTENCE_END, text)
            self.logger.debug(f"Sending TTS sentence_end: {tts_stop}")
            await self.websocket.send(self.protocol.serialize_message(tts_stop))
            
            # Send TTS stop message and reset flag
            tts_stop_final = self.protocol.create_tts_message(self.session_id, TTSState.STOP)
            self.logger.debug(f"Sending TTS stop: {tts_stop_final}")
            await self.websocket.send(self.protocol.serialize_message(tts_stop_final))
            self.tts_started = False  # 重置标志
            
        except Exception as e:
            self.logger.error(f"Failed to send audio data: {e}")

    async def _send_audio_with_timing(self, opus_packets: List[bytes]) -> None:
        # 流控参数优化
        frame_duration = 60  # 帧时长（毫秒），匹配 Opus 编码
        start_time = time.perf_counter()
        play_position = 0
        last_reset_time = time.perf_counter()  # 记录最后的重置时间

        # 检查WebSocket连接状态
        if self.websocket.state in (State.CLOSING, State.CLOSED):
            self.logger.warning("WebSocket connection is closing/closed, cannot send audio")
            return

        try:
            # 预缓冲机制：发送前几帧以减少延迟
            pre_buffer_frames = min(3, len(opus_packets))
            for i in range(pre_buffer_frames):
                if self.websocket.state in (State.CLOSING, State.CLOSED):
                    self.logger.warning(f"Connection closing/closed during pre-buffer at frame {i}")
                    return
                self.logger.info(f"发送预缓冲音频帧: {i}")
                await self.websocket.send(opus_packets[i])
                # 注意：参考项目预缓冲帧之间没有延迟！
            
            remaining_packets = opus_packets[pre_buffer_frames:]

            # 播放剩余音频帧
            for opus_packet in remaining_packets:
                if self.websocket.state in (State.CLOSING, State.CLOSED):
                    self.logger.warning("Connection closing/closed during audio sending")
                    return
                    
                # 每分钟重置一次计时器（虽然对短音频不重要，但保持一致）
                if time.perf_counter() - last_reset_time > 60:
                    last_reset_time = time.perf_counter()

                # 计算预期发送时间
                expected_time = start_time + (play_position / 1000)
                current_time = time.perf_counter()
                delay = expected_time - current_time
                if delay > 0:
                    await asyncio.sleep(delay)

                await self.websocket.send(opus_packet)
                play_position += frame_duration
                
        except Exception as e:
            self.logger.error(f"Error during audio sending: {e}")
            raise

    async def handle_hello_message(self, data: Dict[str, Any]):
        """Handle hello handshake from client"""
        self.logger.info(f"[CLIENT->SERVER] Hello message: {data}")
        self.session_id = str(uuid.uuid4())
        
        response = self.protocol.create_hello_response(self.session_id)
        
        await self.websocket.send(self.protocol.serialize_message(response))
        self.handshake_complete = True
        self.logger.info(f"[SERVER->CLIENT] Hello response sent, session_id: {self.session_id}")
        self.logger.debug(f"[SERVER->CLIENT] Response: {response}")
        
        # Initialize service after handshake
        await self._initialize_service()

    async def handle_client_message(self, message):
        """Handle incoming JSON messages from client"""
        self.logger.info(f"[CLIENT->SERVER] Text message received: {message}")
        try:
            data = self.protocol.parse_message(message)
            if data is None:
                return
                
            msg_type = self.protocol.get_message_type(data)
            self.logger.debug(f"[CLIENT->SERVER] Parsed message type: {msg_type}")
            
            if msg_type == MessageType.HELLO:
                await self.handle_hello_message(data)
            elif msg_type == MessageType.LISTEN:
                state = self.protocol.get_listen_state(data)
                self.logger.info(f"[CLIENT->SERVER] Listen state: {state}")
                
                if state == ListenState.START:
                    self.logger.info("[SERVER] Started listening")
                    
                elif state == ListenState.STOP and self.service:
                    try:
                        # 立即发送 TTS START 消息，让客户端进入等待状态
                        await self._ensure_tts_start()
                        
                        # Handle audio stop - force completion of current audio session
                        if hasattr(self.service, 'handle_client_audio_complete'):
                            self.logger.info("[SERVER] Listen stopped - handling client audio complete")
                            await self.service.handle_client_audio_complete()
                        else:
                            self.logger.warning("[SERVER] Service doesn't support handle_client_audio_complete")
                    except Exception as e:
                        self.logger.error(f"[ERROR] Error handling audio stop: {e}")
                        
                elif state == ListenState.DETECT and self.service:
                    # Handle text content in detect state
                    text_content = data.get("text")
                    if text_content:
                        self.logger.info(f"[CLIENT->SERVER] Detected text: {text_content}")
                        await self.service.handle_text_input(text_content)
                        
            elif msg_type == MessageType.TEXT:
                text_content = self.protocol.get_text_content(data)
                self.logger.info(f"[CLIENT->SERVER] Text message: {text_content}")
                if self.service and text_content:
                    await self.service.handle_text_input(text_content)
            elif msg_type == MessageType.ABORT:
                reason = self.protocol.get_abort_reason(data)
                self.logger.info(f"[CLIENT->SERVER] Abort requested: {reason}")
            else:
                self.logger.warning(f"[CLIENT->SERVER] Unknown message type: {msg_type}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error handling message: {e}")

    async def handle_binary_message(self, data: bytes):
        """Handle binary audio data from client"""
        if self.service:
            await self.service.handle_audio_input(data)
        else:
            self.logger.warning("[SERVER] No service available to handle binary data")

    async def _initialize_service(self):
        """Initialize the service provider"""
        if self.service:
            self.logger.warning("[SERVER] Service already initialized")
            return

        try:
            # Create the service with config and connection handler
            self.service = service_factory.create_service(self.service_type, self.config, self)
            if not self.service:
                raise ValueError(f"Failed to create service of type {self.service_type}")
                
            # Initialize the service with websocket and session ID
            success = await service_factory.initialize_service(
                self.service, self.websocket, self.session_id
            )
            
            if success:
                self.logger.info(f"[SERVER] Service '{self.service_type}' initialized for session {self.session_id}")
                quality_monitor.start_session(self.session_id, self.service_type)
            else:
                raise ValueError(f"Failed to initialize service {self.service_type}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to initialize service: {e}")
            if self.session_id:
                try:
                    quality_monitor.start_session(self.session_id, self.service_type)
                    quality_monitor.end_session(self.session_id, completed=False)
                except Exception as monitor_error:
                    self.logger.error(f"[ERROR] Failed to log failed session: {monitor_error}")
            await self.websocket.close(code=4001, reason=f"Service initialization failed: {e}")

    def _get_disconnect_info(self, exception=None):
        """Get disconnect reason from WebSocket state or exception"""
        
        # Check WebSocket state first
        if hasattr(self.websocket, 'close_code') and self.websocket.close_code is not None:
            close_code = self.websocket.close_code
            close_reason = getattr(self.websocket, 'close_reason', None)
        elif exception and hasattr(exception, 'code'):
            close_code = exception.code
            close_reason = getattr(exception, 'reason', None)
        else:
            return "UNKNOWN_CLOSE"
        
        # If we have a reason, use it; otherwise, generate one based on code
        if close_reason:
            reason_text = close_reason
        else:
            # Generate reason based on close code
            if close_code == 1000:
                reason_text = "客户端正常断开"
            elif close_code == 1001:
                reason_text = "客户端主动断开"
            elif close_code == 1006:
                reason_text = "连接异常断开"
            elif close_code >= 4000:
                reason_text = "服务端主动断开"
            else:
                reason_text = "未知断开原因"
        
        return f"{reason_text}({close_code})"

    async def handle_connection(self):
        """Handle WebSocket connection from mobile client"""
        remote_addr = self.websocket.remote_address
        self.logger.info(f"[CONNECTION] New connection from {remote_addr}")

        try:
            async for message in self.websocket:
                if isinstance(message, str):
                    await self.handle_client_message(message)
                elif isinstance(message, bytes):
                    await self.handle_binary_message(message)
            
            from websockets.protocol import State
            if self.websocket.state in (State.CLOSING, State.CLOSED):
                # Normal completion - check WebSocket state
                disconnect_info = self._get_disconnect_info()
                self.logger.info(f"[DISCONNECT] {disconnect_info} from {remote_addr}")
            else:
                # Unexpected error - check exception
                self.logger.error(f"异常从 WebSocket 循环中退出了")
                    
        except websockets.exceptions.ConnectionClosed as e:
            disconnect_info = self._get_disconnect_info(e)
            log_level = "info" if e.code in (1000, 1001) else "warning"
            getattr(self.logger, log_level)(f"[DISCONNECT] {disconnect_info} from {remote_addr}")
            
        except websockets.exceptions.InvalidState as e:
            disconnect_info = f"Invalid WebSocket state: {e}"
            self.logger.error(f"[ERROR] {disconnect_info} from {remote_addr}")
            
        except Exception as e:
            disconnect_info = f"Unexpected error: {type(e).__name__}: {e}"
            self.logger.error(f"[ERROR] {disconnect_info} from {remote_addr}")
            
        finally:
            # Log final connection status
            session_info = f" (Session: {self.session_id})" if self.session_id else " (No session established)"
            self.logger.info(f"[CONNECTION CLOSED] Client {remote_addr}{session_info} - {disconnect_info}")
            
            # Handle connection closed first to stop ongoing operations
            if self.service:
                try:
                    await self.service.handle_connection_closed()
                except Exception as e:
                    self.logger.error(f"Error handling connection closed: {e}")
            
            # Cleanup resources
            if self.service:
                await self.service.cleanup()
            elif self.session_id:
                quality_monitor.end_session(self.session_id, completed=False)
