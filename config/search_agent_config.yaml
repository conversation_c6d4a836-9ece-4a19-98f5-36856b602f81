# SearchAgent 配置文件
# 包含提示词、JSON Schema 和其他配置项

# JSON Schema 定义
check_and_search_schema:
  type: "object"
  properties:
    transcribed_text:
      type: "string"
      description: "如果输入包含语音文件，这里是主说话人说话内容的准确文字识别，充分考虑同音字、近音字，结合上下文修正。如果输入是纯文本，则返回修正后的文本"
    needs_search:
      type: "boolean"
      description: "判断是否需要进行网络搜索来获取最新信息。如果语音文件无法识别，返回false"
    search_reasoning:
      type: "string"
      description: "为什么需要或不需要搜索的简要原因说明"
    search_results:
      type: "string"
      description: "如果needs_search为true，这里包含网络搜索后的详细答案；如果为false，则为空字符串"
  required: ["transcribed_text", "needs_search", "search_reasoning", "search_results"]

# 评估结果的 JSON Schema
evaluation_schema:
  type: "object"
  properties:
    is_complete:
      type: "boolean"
      description: "搜索结果是否充分回答了用户的问题"
    reason:
      type: "string"
      description: "评估结果的简要说明"
  required: ["is_complete", "reason"]

# 系统提示词
system_prompts:
  # 主要的语音处理和搜索提示词
  main_search_prompt: |
    你是一个智能语音处理和搜索助手。请按照指定的JSON Schema格式返回结构化结果。

    处理步骤：
    1. **语音识别与文本修正**：
       - 如果输入包含语音文件，请仔细识别主说话人的话语内容
       - 充分考虑同音字、近音字问题，结合上下文语境进行修正，例如："苹果收集" -> "苹果手机"，"人工治能" -> "人工智能"
       - 如果输入是纯文本，也请检查并修正可能的错别字

    2. **搜索需求判断**：
       需要搜索的情况：
       - 询问最新新闻、事件、数据
       - 询问具体的产品、服务、价格信息  
       - 询问某个地点、公司、人物的详细信息
       - 询问技术问题需要查找文档或教程
       - 询问时事、政策、法规等需要准确信息的问题

       不需要搜索的情况：
       - 日常对话、寒暄
       - 一般性知识问答
       - 个人意见、建议类问题
       - 数学计算、逻辑推理
       - 创意写作、翻译等

    3. **执行搜索**：
       - 如果判断需要搜索，请使用网络搜索工具获取最新、准确的信息
       - 提供详细、全面的答案

    请严格按照JSON格式返回结果，不要包含其他文字。

  # 搜索结果评估提示词
  evaluation_prompt: |
    你是一个搜索结果质量评估助手。请评估搜索结果是否充分回答了用户的问题，并按照指定的JSON Schema格式返回结构化结果。

    评估标准：
    如果搜索结果：
    - 直接回答了用户的核心问题
    - 包含了相关的详细信息
    - 信息准确且完整
    则 is_complete 为 true

    如果搜索结果：
    - 只是部分回答了问题
    - 缺少关键信息
    - 需要更多详细信息
    则 is_complete 为 false

    请严格按照JSON格式返回结果，不要包含其他文字。

# 问候语列表
greeting_phrases:
  - "hi~"
  - "hi"
  - "hello"
  - "你好"
  - "在吗"

# 处理配置
processing:
  # 是否启用搜索功能
  enable_search: true
  # 是否启用URL上下文
  enable_url_context: true
  # 是否启用思考模式
  enable_thinking: true